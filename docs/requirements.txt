# Sphinx Documentation Requirements
# Install with: pip install -r requirements.txt

# Core Sphinx
sphinx>=7.2.0
sphinx-rtd-theme>=2.0.0
furo>=2024.1.29

# Extensions
myst-parser>=2.0.0
sphinx-copybutton>=0.5.2
sphinx-design>=0.5.0
sphinx-tabs>=3.4.1
sphinx-autobuild>=2024.2.4

# Additional utilities
sphinx-external-toc>=1.0.1
sphinx-togglebutton>=0.3.2
sphinx-inline-tabs>=2023.4.21

# Development tools
doc8>=1.1.1
rstcheck>=6.2.0
linkchecker>=10.2.1

# Optional enhancements
sphinx-notfound-page>=1.0.0
sphinx-sitemap>=2.5.1
sphinxext-opengraph>=0.9.1

# TypeScript documentation support
sphinx-js>=3.2.2
jsdoc>=4.0.0
