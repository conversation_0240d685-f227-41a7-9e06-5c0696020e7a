#!/usr/bin/env python3
"""
Swiss Budget Pro Documentation Validation Script

This script validates the documentation structure and content
without requiring <PERSON>phinx to be installed.
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Tuple

def check_file_exists(filepath: str) -> bool:
    """Check if a file exists."""
    return Path(filepath).exists()

def check_markdown_syntax(filepath: str) -> List[str]:
    """Basic markdown syntax validation."""
    errors = []
    
    if not check_file_exists(filepath):
        return [f"File not found: {filepath}"]
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        # Check for basic markdown issues
        for i, line in enumerate(lines, 1):
            # Check for unmatched code blocks
            if line.strip().startswith('```') and not line.strip().endswith('```'):
                # Count code block markers
                code_blocks = content.count('```')
                if code_blocks % 2 != 0:
                    errors.append(f"{filepath}:{i}: Unmatched code block")
            
            # Check for broken internal links
            if '](../' in line or '](/' in line:
                # Extract link target
                link_match = re.search(r'\]\(([^)]+)\)', line)
                if link_match:
                    link_target = link_match.group(1)
                    if link_target.startswith('../') or link_target.startswith('/'):
                        # Convert to relative path
                        if link_target.startswith('../'):
                            target_path = Path(filepath).parent / link_target
                        else:
                            target_path = Path('.') / link_target.lstrip('/')

                        # Try with .md extension if file doesn't exist
                        if not target_path.exists():
                            target_path_md = target_path.with_suffix('.md')
                            if not target_path_md.exists():
                                errors.append(f"{filepath}:{i}: Broken link: {link_target}")
    
    except Exception as e:
        errors.append(f"{filepath}: Error reading file: {e}")
    
    return errors

def validate_toctree_references(filepath: str) -> List[str]:
    """Validate toctree references in index files."""
    errors = []
    
    if not check_file_exists(filepath):
        return [f"File not found: {filepath}"]
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find toctree blocks
        toctree_pattern = r'```\{toctree\}.*?\n(.*?)```'
        toctree_matches = re.findall(toctree_pattern, content, re.DOTALL)
        
        for toctree_content in toctree_matches:
            lines = toctree_content.strip().split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith(':') and not line.startswith('#'):
                    # This should be a file reference
                    if '/' in line:
                        ref_path = Path(filepath).parent / f"{line}.md"
                    else:
                        ref_path = Path(filepath).parent / f"{line}.md"
                    
                    if not ref_path.exists():
                        errors.append(f"{filepath}: Toctree reference not found: {line}")
    
    except Exception as e:
        errors.append(f"{filepath}: Error validating toctree: {e}")
    
    return errors

def check_static_files() -> List[str]:
    """Check that required static files exist."""
    errors = []
    required_files = [
        '_static/custom.css',
        '_static/custom.js',
        '_static/logo-light.svg',
        '_static/logo-dark.svg',
        '_static/favicon.svg'
    ]
    
    for file_path in required_files:
        if not check_file_exists(file_path):
            errors.append(f"Missing static file: {file_path}")
    
    return errors

def check_configuration() -> List[str]:
    """Check Sphinx configuration."""
    errors = []
    
    if not check_file_exists('conf.py'):
        errors.append("Missing conf.py configuration file")
        return errors
    
    try:
        with open('conf.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for required configuration
        required_configs = [
            'project = ',
            'extensions = ',
            'html_theme = ',
            'html_static_path = '
        ]
        
        for config in required_configs:
            if config not in content:
                errors.append(f"Missing configuration: {config}")
    
    except Exception as e:
        errors.append(f"Error reading conf.py: {e}")
    
    return errors

def main():
    """Main validation function."""
    print("🔍 Validating Swiss Budget Pro Documentation...")
    print("=" * 50)
    
    all_errors = []
    
    # Check configuration
    print("📋 Checking configuration...")
    config_errors = check_configuration()
    all_errors.extend(config_errors)
    if config_errors:
        for error in config_errors:
            print(f"  ❌ {error}")
    else:
        print("  ✅ Configuration OK")
    
    # Check static files
    print("\n🎨 Checking static files...")
    static_errors = check_static_files()
    all_errors.extend(static_errors)
    if static_errors:
        for error in static_errors:
            print(f"  ❌ {error}")
    else:
        print("  ✅ Static files OK")
    
    # Check main documentation files
    print("\n📚 Checking documentation files...")
    doc_files = [
        'index.md',
        'getting-started.md',
        'user-guide/index.md',
        'user-guide/smart-dashboard.md',
        'api-reference/index.md',
        'changelog.md'
    ]
    
    for doc_file in doc_files:
        print(f"  📄 Checking {doc_file}...")
        
        # Basic syntax check
        syntax_errors = check_markdown_syntax(doc_file)
        all_errors.extend(syntax_errors)
        
        # Toctree validation for index files
        if doc_file.endswith('index.md'):
            toctree_errors = validate_toctree_references(doc_file)
            all_errors.extend(toctree_errors)
        
        if syntax_errors or (doc_file.endswith('index.md') and toctree_errors):
            for error in syntax_errors + (toctree_errors if doc_file.endswith('index.md') else []):
                print(f"    ❌ {error}")
        else:
            print(f"    ✅ {doc_file} OK")
    
    # Summary
    print("\n" + "=" * 50)
    if all_errors:
        print(f"❌ Validation completed with {len(all_errors)} errors:")
        for error in all_errors:
            print(f"  • {error}")
        sys.exit(1)
    else:
        print("✅ All documentation validation checks passed!")
        print("\n🎉 Documentation is ready for building!")
        print("\nNext steps:")
        print("  1. Install Sphinx: pip install -r requirements.txt")
        print("  2. Build docs: make html")
        print("  3. Serve docs: make serve")

if __name__ == "__main__":
    main()
