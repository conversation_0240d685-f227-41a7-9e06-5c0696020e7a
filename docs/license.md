# License

Swiss Budget Pro is released under the MIT License, ensuring it remains free and open-source for the Swiss FIRE community.

## MIT License

```
MIT License

Copyright (c) 2024 Swiss Budget Pro Team

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## What This Means

### ✅ You Can

- **Use** Swiss Budget Pro for personal or commercial purposes
- **Modify** the source code to fit your needs
- **Distribute** copies of the software
- **Sublicense** the software under compatible terms
- **Sell** copies or services based on the software
- **Create derivative works** based on the software

### 📋 You Must

- **Include the license** in any copies or substantial portions
- **Include the copyright notice** in any copies or substantial portions
- **Provide attribution** when redistributing or modifying

### ❌ You Cannot

- **Hold the authors liable** for any damages or issues
- **Use the authors' names** for endorsement without permission
- **Remove or modify** the license and copyright notices

## Third-Party Licenses

Swiss Budget Pro includes several third-party libraries and components. Here are the key dependencies and their licenses:

### Core Dependencies

```{list-table} Main Dependencies
:header-rows: 1
:widths: 30 20 50

* - Library
  - License
  - Purpose
* - React
  - MIT
  - User interface framework
* - TypeScript
  - Apache 2.0
  - Type-safe JavaScript
* - Vite
  - MIT
  - Build tool and development server
* - Tailwind CSS
  - MIT
  - Utility-first CSS framework
* - D3.js
  - BSD 3-Clause
  - Data visualization library
* - Decimal.js
  - MIT
  - Precise decimal arithmetic
* - React i18next
  - MIT
  - Internationalization framework
```

### Development Dependencies

```{list-table} Development Tools
:header-rows: 1
:widths: 30 20 50

* - Library
  - License
  - Purpose
* - Vitest
  - MIT
  - Unit testing framework
* - Playwright
  - Apache 2.0
  - End-to-end testing
* - ESLint
  - MIT
  - Code linting and quality
* - Prettier
  - MIT
  - Code formatting
* - PostCSS
  - MIT
  - CSS processing
```

### Documentation Dependencies

```{list-table} Documentation Tools
:header-rows: 1
:widths: 30 20 50

* - Library
  - License
  - Purpose
* - Sphinx
  - BSD 2-Clause
  - Documentation generator
* - MyST Parser
  - MIT
  - Markdown parser for Sphinx
* - Furo Theme
  - MIT
  - Modern Sphinx theme
* - Sphinx Design
  - MIT
  - Design components for Sphinx
```

## Data and Content Licenses

### Swiss Financial Data

Swiss Budget Pro uses publicly available Swiss financial data:

- **Tax Rates**: Based on official Swiss Federal Tax Administration data (public domain)
- **Canton Information**: Compiled from official cantonal websites (public domain)
- **Economic Indicators**: References to publicly available SNB and SIX data
- **Legal Framework**: Based on publicly available Swiss laws and regulations

### User-Generated Content

- **User Data**: Remains the property of the user
- **Scenarios**: Created by users belong to the users
- **Contributions**: Code contributions are licensed under MIT
- **Documentation**: User-contributed documentation is licensed under MIT

## Attribution Requirements

### For Redistribution

When redistributing Swiss Budget Pro or derivative works:

```html
<!-- Include in your application -->
<div class="attribution">
  Powered by <a href="https://github.com/swiss-budget-pro/swiss-budget-pro">
    Swiss Budget Pro
  </a> - Open Source Swiss FIRE Planning Tool
</div>
```

### For Modifications

When creating derivative works:

```markdown
# Your Project Name

Based on Swiss Budget Pro (https://github.com/swiss-budget-pro/swiss-budget-pro)
Licensed under MIT License

## Modifications
- List your key modifications here
- Explain how your version differs
- Maintain link to original project
```

### For Commercial Use

Commercial use is permitted and encouraged:

- **SaaS Applications**: You can build commercial services using Swiss Budget Pro
- **Consulting Services**: You can offer paid consulting using the tool
- **Educational Use**: You can use it in paid courses or training
- **Enterprise Deployment**: Companies can deploy internally or for clients

**Requirements for Commercial Use:**
- Include the MIT license in your distribution
- Provide attribution to Swiss Budget Pro
- Do not claim ownership of the original codebase
- Consider contributing improvements back to the community

## Warranty Disclaimer

```{admonition} ⚠️ Important Disclaimer
:class: warning

**No Warranty**: Swiss Budget Pro is provided "as is" without warranty of any kind.

**Financial Decisions**: This software is for informational purposes only and does not constitute financial advice.

**Accuracy**: While we strive for accuracy, users should verify all calculations and consult with qualified professionals.

**Liability**: The authors and contributors are not liable for any financial decisions made using this software.
```

## Contributing and License

### Contributor License Agreement

By contributing to Swiss Budget Pro, you agree that:

- Your contributions will be licensed under the MIT License
- You have the right to license your contributions
- You grant the project maintainers the right to use your contributions
- You understand that your contributions may be modified or redistributed

### Code Ownership

- **Original Code**: Owned by the Swiss Budget Pro Team
- **Contributions**: Owned by contributors, licensed to the project
- **Derivative Works**: Subject to MIT License terms
- **User Data**: Always remains with the user

## License Compatibility

The MIT License is compatible with:

### ✅ Compatible Licenses

- **Apache 2.0**: Can combine with Apache-licensed code
- **BSD Licenses**: Compatible with BSD 2-Clause and 3-Clause
- **ISC License**: Functionally equivalent to MIT
- **Public Domain**: Can incorporate public domain code
- **Creative Commons**: Compatible with CC0 and some CC licenses

### ⚠️ Potentially Incompatible

- **GPL v2/v3**: May require entire project to be GPL (consult legal advice)
- **AGPL**: Network use may trigger copyleft requirements
- **Proprietary**: Cannot incorporate proprietary code without permission

## Frequently Asked Questions

### Can I use Swiss Budget Pro in my commercial product?

Yes, the MIT License explicitly allows commercial use. You can:
- Integrate it into commercial software
- Offer it as a service
- Modify it for commercial purposes
- Charge for services built on top of it

### Do I need to open-source my modifications?

No, the MIT License does not require you to open-source modifications. However:
- You must include the original license
- You must provide attribution
- We encourage contributing improvements back to the community

### Can I remove the Swiss Budget Pro branding?

Yes, you can modify or remove branding, but you must:
- Keep the license and copyright notices
- Provide attribution in your documentation or about page
- Not claim ownership of the original codebase

### What about the Swiss financial data used?

The Swiss financial data we reference is publicly available and not copyrighted:
- Tax rates are from official government sources
- Canton information is public domain
- Economic indicators are publicly available
- Legal frameworks are public information

### Can I create a competing product?

Yes, the MIT License allows you to create competing products. We encourage innovation in the Swiss FIRE planning space and believe competition benefits users.

## Contact

For license-related questions:

- **General Questions**: Open a GitHub Discussion
- **Legal Concerns**: Contact the maintainers through GitHub
- **Commercial Licensing**: Standard MIT License applies to all use cases
- **Contribution Questions**: See our [Contributing Guide](contributing.md)

---

*The MIT License ensures Swiss Budget Pro remains free and accessible to the Swiss FIRE community while allowing for commercial innovation and derivative works.*
