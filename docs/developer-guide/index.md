# Developer Guide

Welcome to the Swiss Budget Pro Developer Guide. This comprehensive resource covers everything you need to know to contribute to, extend, or integrate with Swiss Budget Pro.

## Overview

Swiss Budget Pro is built with modern web technologies and follows best practices for maintainable, scalable financial software. This guide provides technical documentation for developers, contributors, and integrators.

```{admonition} 🚀 Quick Start for Developers
:class: tip

**Get Started in 5 Minutes:**
1. Clone the repository
2. Install dependencies: `npm install`
3. Start development server: `npm run dev`
4. Open browser to `http://localhost:5173`
5. Start coding!
```

## Technology Stack

### Core Technologies

```{grid} 1 2 2 2
:gutter: 3

```{grid-item-card} ⚛️ Frontend Framework
**React 18**
- Functional components with hooks
- Context API for state management
- Concurrent features
- Strict mode enabled
```

```{grid-item-card} 📘 Type Safety
**TypeScript 5.0+**
- Strict type checking
- Comprehensive interfaces
- Generic type utilities
- Advanced type patterns
```

```{grid-item-card} ⚡ Build Tool
**Vite 5.0+**
- Lightning-fast HMR
- Optimized production builds
- Plugin ecosystem
- Modern ES modules
```

```{grid-item-card} 🎨 Styling
**Tailwind CSS 3.0+**
- Utility-first approach
- Custom Swiss design system
- Responsive design
- Dark mode support
```

### Specialized Libraries

```{list-table} Key Dependencies
:header-rows: 1
:widths: 25 25 50

* - Library
  - Version
  - Purpose
* - D3.js
  - ^7.8.0
  - Data visualization and charts
* - React i18next
  - ^13.0.0
  - Internationalization
* - Decimal.js
  - ^10.4.0
  - Precise financial calculations
* - Vitest
  - ^1.0.0
  - Unit testing framework
* - Playwright
  - ^1.40.0
  - End-to-end testing
```

## Project Structure

```{code-block} text
swiss-budget-pro/
├── src/
│   ├── components/          # React components
│   │   ├── common/         # Reusable UI components
│   │   ├── charts/         # D3.js visualization components
│   │   ├── forms/          # Form components
│   │   └── layout/         # Layout components
│   ├── hooks/              # Custom React hooks
│   ├── utils/              # Utility functions
│   ├── types/              # TypeScript type definitions
│   ├── constants/          # Application constants
│   ├── i18n/              # Internationalization
│   └── styles/            # Global styles
├── test/                   # Test files
│   ├── unit/              # Unit tests
│   ├── integration/       # Integration tests
│   └── e2e/               # End-to-end tests
├── docs/                   # Documentation
├── public/                 # Static assets
└── scripts/               # Build and utility scripts
```

## Development Environment Setup

### Prerequisites

```{list-table} System Requirements
:header-rows: 1
:widths: 25 25 50

* - Tool
  - Version
  - Purpose
* - Node.js
  - 18.0+
  - JavaScript runtime
* - npm
  - 9.0+
  - Package manager
* - Git
  - 2.30+
  - Version control
* - VS Code
  - Latest
  - Recommended editor
```

### Installation Steps

1. **Clone Repository**
   ```bash
   git clone https://github.com/swiss-budget-pro/swiss-budget-pro.git
   cd swiss-budget-pro
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your settings
   ```

4. **Start Development Server**
   ```bash
   npm run dev
   ```

### Development Scripts

```{list-table} Available Scripts
:header-rows: 1
:widths: 25 75

* - Command
  - Description
* - `npm run dev`
  - Start development server with HMR
* - `npm run build`
  - Build production bundle
* - `npm run preview`
  - Preview production build locally
* - `npm run test`
  - Run unit tests with Vitest
* - `npm run test:e2e`
  - Run end-to-end tests with Playwright
* - `npm run lint`
  - Run ESLint for code quality
* - `npm run type-check`
  - Run TypeScript type checking
* - `npm run format`
  - Format code with Prettier
```

## Architecture Overview

### Component Architecture

```{mermaid}
graph TD
    A[App Component] --> B[Layout Components]
    A --> C[Route Components]
    
    B --> D[Header]
    B --> E[Navigation]
    B --> F[Footer]
    
    C --> G[Dashboard]
    C --> H[Planning]
    C --> I[Analytics]
    C --> J[Settings]
    
    G --> K[Summary Cards]
    G --> L[Quick Actions]
    G --> M[Recent Activity]
    
    H --> N[Income/Expense Forms]
    H --> O[Goal Setting]
    H --> P[Scenario Management]
    
    I --> Q[Charts]
    I --> R[Reports]
    I --> S[Projections]
```

### State Management

Swiss Budget Pro uses React Context for state management:

```{code-block} typescript
// Core contexts
interface AppContexts {
  UserContext: UserProfile & UserActions;
  FinancialContext: FinancialData & FinancialActions;
  SettingsContext: AppSettings & SettingsActions;
  CalculationContext: CalculationResults & CalculationActions;
}

// Context providers wrap the application
<UserProvider>
  <FinancialProvider>
    <SettingsProvider>
      <CalculationProvider>
        <App />
      </CalculationProvider>
    </SettingsProvider>
  </FinancialProvider>
</UserProvider>
```

### Data Flow

```{mermaid}
sequenceDiagram
    participant U as User
    participant C as Component
    participant H as Hook
    participant S as State
    participant L as LocalStorage
    
    U->>C: Input Change
    C->>H: Update Hook
    H->>S: Update State
    S->>L: Auto-save
    S->>C: Re-render
    C->>U: Updated UI
```

## Core Components

### Financial Calculation Engine

The heart of Swiss Budget Pro is its calculation engine:

```{code-block} typescript
interface CalculationEngine {
  // Core calculations
  calculateNetWorth(assets: Asset[], liabilities: Liability[]): number;
  calculateSavingsRate(income: number, expenses: number): number;
  calculateFIREProgress(currentWealth: number, fireGoal: number): number;
  
  // Swiss-specific calculations
  calculateSwissTax(income: number, canton: Canton): TaxResult;
  calculatePillar3aBenefit(contribution: number, taxRate: number): number;
  
  // Projections
  projectWealth(params: ProjectionParams): WealthProjection[];
  calculateFIRETimeline(params: FIREParams): FIRETimeline;
}
```

### Chart Components

D3.js-powered visualizations:

```{code-block} typescript
interface ChartComponents {
  WealthProjectionChart: React.FC<WealthProjectionProps>;
  SavingsRateChart: React.FC<SavingsRateProps>;
  TaxOptimizationChart: React.FC<TaxOptimizationProps>;
  FIREProgressChart: React.FC<FIREProgressProps>;
}

// Example chart component
const WealthProjectionChart: React.FC<WealthProjectionProps> = ({
  data,
  width = 800,
  height = 400
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  
  useEffect(() => {
    if (!svgRef.current || !data) return;
    
    const svg = d3.select(svgRef.current);
    // D3.js chart implementation
  }, [data, width, height]);
  
  return <svg ref={svgRef} width={width} height={height} />;
};
```

### Custom Hooks

Reusable logic encapsulated in custom hooks:

```{code-block} typescript
// Financial calculations hook
export const useFinancialCalculations = () => {
  const { financialData } = useFinancialContext();
  
  const netWorth = useMemo(() => 
    calculateNetWorth(financialData.assets, financialData.liabilities),
    [financialData]
  );
  
  const savingsRate = useMemo(() =>
    calculateSavingsRate(financialData.income, financialData.expenses),
    [financialData]
  );
  
  return { netWorth, savingsRate };
};

// Local storage hook
export const useLocalStorage = <T>(key: string, defaultValue: T) => {
  const [value, setValue] = useState<T>(() => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch {
      return defaultValue;
    }
  });
  
  const setStoredValue = useCallback((newValue: T) => {
    setValue(newValue);
    localStorage.setItem(key, JSON.stringify(newValue));
  }, [key]);
  
  return [value, setStoredValue] as const;
};
```

## Testing Strategy

### Testing Pyramid

```{mermaid}
graph TD
    A[E2E Tests] --> B[Integration Tests]
    B --> C[Unit Tests]
    
    A --> A1[User Workflows]
    A --> A2[Cross-browser Testing]
    A --> A3[Performance Testing]
    
    B --> B1[Component Integration]
    B --> B2[API Integration]
    B --> B3[State Management]
    
    C --> C1[Pure Functions]
    C --> C2[Component Logic]
    C --> C3[Utility Functions]
```

### Unit Testing

Using Vitest for fast, reliable unit tests:

```{code-block} typescript
// Example unit test
import { describe, it, expect } from 'vitest';
import { calculateSavingsRate } from '../utils/calculations';

describe('calculateSavingsRate', () => {
  it('calculates savings rate correctly', () => {
    const income = 10000;
    const expenses = 7000;
    const expected = 0.3; // 30%
    
    expect(calculateSavingsRate(income, expenses)).toBe(expected);
  });
  
  it('handles edge cases', () => {
    expect(calculateSavingsRate(0, 0)).toBe(0);
    expect(calculateSavingsRate(1000, 1000)).toBe(0);
    expect(calculateSavingsRate(1000, 1500)).toBe(-0.5);
  });
});
```

### Integration Testing

Testing component interactions:

```{code-block} typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { FinancialPlanningForm } from '../components/FinancialPlanningForm';

describe('FinancialPlanningForm', () => {
  it('updates calculations when income changes', async () => {
    render(<FinancialPlanningForm />);
    
    const incomeInput = screen.getByLabelText('Monthly Income');
    fireEvent.change(incomeInput, { target: { value: '8000' } });
    
    // Verify calculations update
    expect(await screen.findByText(/Savings Rate: 25%/)).toBeInTheDocument();
  });
});
```

### End-to-End Testing

Playwright tests for complete user workflows:

```{code-block} typescript
import { test, expect } from '@playwright/test';

test('complete FIRE planning workflow', async ({ page }) => {
  await page.goto('/');
  
  // Fill in basic information
  await page.fill('[data-testid="income-input"]', '120000');
  await page.fill('[data-testid="expenses-input"]', '80000');
  
  // Navigate to projections
  await page.click('[data-testid="projections-tab"]');
  
  // Verify FIRE calculation
  await expect(page.locator('[data-testid="fire-age"]')).toContainText('55');
});
```

## Code Quality Standards

### TypeScript Configuration

```{code-block} json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true
  }
}
```

### ESLint Rules

Key linting rules for code quality:

```{code-block} json
{
  "extends": [
    "@typescript-eslint/recommended",
    "plugin:react-hooks/recommended"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "react-hooks/exhaustive-deps": "error",
    "prefer-const": "error",
    "no-var": "error"
  }
}
```

### Prettier Configuration

Consistent code formatting:

```{code-block} json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```

## Performance Optimization

### Bundle Optimization

- **Code splitting**: Route-based and component-based splitting
- **Tree shaking**: Eliminate unused code
- **Lazy loading**: Load components on demand
- **Asset optimization**: Compress images and fonts

### Runtime Performance

```{code-block} typescript
// Memoization for expensive calculations
const expensiveCalculation = useMemo(() => {
  return complexFinancialCalculation(data);
}, [data]);

// Debounced input handling
const debouncedUpdate = useDebouncedCallback(
  (value: string) => updateCalculations(value),
  300
);

// Virtual scrolling for large lists
const VirtualizedList = ({ items }: { items: any[] }) => {
  return (
    <FixedSizeList
      height={400}
      itemCount={items.length}
      itemSize={50}
    >
      {({ index, style }) => (
        <div style={style}>{items[index]}</div>
      )}
    </FixedSizeList>
  );
};
```

## Contributing Guidelines

### Development Workflow

1. **Fork and Clone**
   ```bash
   git clone https://github.com/your-username/swiss-budget-pro.git
   ```

2. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make Changes**
   - Follow coding standards
   - Add tests for new features
   - Update documentation

4. **Test Changes**
   ```bash
   npm run test
   npm run test:e2e
   npm run lint
   ```

5. **Submit Pull Request**
   - Clear description
   - Link to related issues
   - Include screenshots if UI changes

### Commit Message Format

```
type(scope): description

[optional body]

[optional footer]
```

Examples:
- `feat(calculations): add Swiss tax optimization engine`
- `fix(charts): resolve D3.js memory leak in projections`
- `docs(api): update calculation engine documentation`

## API Documentation

### Core Interfaces

```{code-block} typescript
interface UserProfile {
  personalInfo: PersonalInfo;
  financialGoals: FinancialGoals;
  preferences: UserPreferences;
}

interface FinancialData {
  income: IncomeSource[];
  expenses: ExpenseCategory[];
  assets: Asset[];
  liabilities: Liability[];
  investments: Investment[];
}

interface CalculationResults {
  netWorth: number;
  savingsRate: number;
  fireProgress: number;
  projections: WealthProjection[];
  taxOptimization: TaxOptimization;
}
```

### Calculation Functions

Detailed API documentation for all calculation functions is available in the [API Reference](../api-reference/index.md) section.

## Deployment

### Build Process

```bash
# Production build
npm run build

# Preview build locally
npm run preview

# Type checking
npm run type-check

# Linting
npm run lint
```

### Environment Variables

```{code-block} bash
# .env.production
VITE_APP_VERSION=1.0.0
VITE_BUILD_DATE=2024-01-01
VITE_ENVIRONMENT=production
```

---

*This developer guide is continuously updated. For the latest information, check the repository documentation and contribute improvements through pull requests.*
