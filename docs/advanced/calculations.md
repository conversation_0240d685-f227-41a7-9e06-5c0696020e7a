# Advanced Calculations

Swiss Budget Pro implements sophisticated financial calculations designed for precision, performance, and Swiss-specific requirements. This guide covers the mathematical foundations and implementation details.

## Overview

The calculation engine is built on proven financial mathematics with Swiss-specific adaptations. All calculations use high-precision decimal arithmetic to avoid floating-point errors common in financial software.

```{admonition} 🧮 Calculation Principles
:class: tip

**Precision**: Decimal.js for exact financial calculations
**Performance**: Memoized results and optimized algorithms
**Accuracy**: Validated against Swiss financial standards
**Transparency**: Open-source algorithms you can verify
**Flexibility**: Configurable parameters for different scenarios
```

## Core Financial Mathematics

### Compound Interest Calculations

The foundation of FIRE planning is compound interest. Swiss Budget Pro uses the standard compound interest formula with Swiss-specific adaptations:

```typescript
/**
 * Future Value with Compound Interest
 * FV = PV * (1 + r)^n + PMT * [((1 + r)^n - 1) / r]
 */
function calculateFutureValue(
  presentValue: Decimal,
  monthlyPayment: Decimal,
  annualRate: Decimal,
  years: number
): Decimal {
  const monthlyRate = annualRate.div(12);
  const totalMonths = years * 12;

  // Compound growth of initial amount
  const compoundedPV = presentValue.mul(
    monthlyRate.plus(1).pow(totalMonths)
  );

  // Future value of annuity (monthly contributions)
  const annuityFV = monthlyPayment.mul(
    monthlyRate.plus(1).pow(totalMonths).minus(1).div(monthlyRate)
  );

  return compoundedPV.plus(annuityFV);
}
```

### Present Value Calculations

For FIRE planning, we need to determine how much money is needed today to support future expenses:

```typescript
/**
 * Present Value of Future Expenses
 * PV = FV / (1 + r)^n
 */
function calculatePresentValue(
  futureValue: Decimal,
  discountRate: Decimal,
  years: number
): Decimal {
  return futureValue.div(
    discountRate.plus(1).pow(years)
  );
}

/**
 * FIRE Number Calculation
 * Uses the 4% rule with Swiss adjustments and continuing income
 */
function calculateFIRENumber(
  annualExpenses: Decimal,
  continuingAnnualIncome: Decimal = new Decimal(0),
  withdrawalRate: Decimal = new Decimal(0.04),
  safetyMargin: Decimal = new Decimal(1.25)
): Decimal {
  // Net expenses after continuing income
  const netExpenses = Decimal.max(
    annualExpenses.minus(continuingAnnualIncome),
    new Decimal(0)
  );

  return netExpenses
    .div(withdrawalRate)
    .mul(safetyMargin); // 25% safety margin for Swiss market conditions
}

/**
 * Calculate continuing income that persists after retirement
 */
function calculateContinuingIncome(
  companyIncome: Decimal,
  rentalIncome: Decimal,
  investmentIncome: Decimal,
  years: number,
  growthRate: Decimal = new Decimal(0.03)
): Decimal {
  const totalCurrentIncome = companyIncome
    .plus(rentalIncome)
    .plus(investmentIncome);

  // Project income growth over time
  return totalCurrentIncome.mul(
    growthRate.plus(1).pow(years)
  );
}
```

## Swiss Tax Calculations

### Federal Tax Calculation

Swiss federal tax uses a progressive rate structure:

```typescript
interface TaxBracket {
  min: number;
  max: number;
  rate: number;
  base: number;
}

const FEDERAL_TAX_BRACKETS_2024: TaxBracket[] = [
  { min: 0, max: 14500, rate: 0, base: 0 },
  { min: 14500, max: 31600, rate: 0.77, base: 0 },
  { min: 31600, max: 41400, rate: 0.88, base: 131.65 },
  { min: 41400, max: 55200, rate: 2.64, base: 217.93 },
  { min: 55200, max: 72500, rate: 2.97, base: 582.21 },
  { min: 72500, max: 78100, rate: 5.94, base: 1096.32 },
  // ... additional brackets
];

function calculateFederalTax(taxableIncome: Decimal): TaxResult {
  let totalTax = new Decimal(0);
  let remainingIncome = taxableIncome;

  for (const bracket of FEDERAL_TAX_BRACKETS_2024) {
    if (remainingIncome.lte(0)) break;

    const bracketMin = new Decimal(bracket.min);
    const bracketMax = new Decimal(bracket.max);
    const bracketRate = new Decimal(bracket.rate).div(100);

    if (taxableIncome.gt(bracketMin)) {
      const taxableInBracket = Decimal.min(
        remainingIncome,
        bracketMax.minus(bracketMin)
      );

      const bracketTax = taxableInBracket.mul(bracketRate);
      totalTax = totalTax.plus(bracketTax);
      remainingIncome = remainingIncome.minus(taxableInBracket);
    }
  }

  return {
    amount: totalTax,
    effectiveRate: totalTax.div(taxableIncome),
    marginalRate: getMarginalRate(taxableIncome)
  };
}
```

### Cantonal Tax Calculation

Each canton has its own tax calculation method:

```typescript
interface CantonTaxConfig {
  code: string;
  multiplier: Decimal;
  baseTax: TaxBracket[];
  deductions: CantonDeduction[];
  wealthTaxRate: Decimal;
}

function calculateCantonalTax(
  taxableIncome: Decimal,
  canton: CantonTaxConfig,
  municipality?: MunicipalityConfig
): TaxResult {
  // Calculate base cantonal tax
  const baseTax = calculateProgressiveTax(taxableIncome, canton.baseTax);

  // Apply cantonal multiplier
  const cantonalTax = baseTax.mul(canton.multiplier);

  // Add municipal tax if applicable
  const municipalTax = municipality
    ? cantonalTax.mul(municipality.multiplier)
    : new Decimal(0);

  return {
    cantonal: cantonalTax,
    municipal: municipalTax,
    total: cantonalTax.plus(municipalTax)
  };
}
```

### Wealth Tax Calculation

Swiss wealth tax is calculated on net worth:

```typescript
function calculateWealthTax(
  netWorth: Decimal,
  canton: CantonCode,
  maritalStatus: MaritalStatus
): Decimal {
  const config = getWealthTaxConfig(canton);
  const exemption = getWealthTaxExemption(canton, maritalStatus);

  const taxableWealth = Decimal.max(
    netWorth.minus(exemption),
    new Decimal(0)
  );

  return calculateProgressiveTax(taxableWealth, config.brackets);
}
```

## Pillar 3a Optimization

### Contribution Optimization

```typescript
function optimizePillar3aContribution(
  income: Decimal,
  currentContribution: Decimal,
  marginalTaxRate: Decimal,
  employmentType: EmploymentType
): Pillar3aOptimization {
  const maxContribution = getMaxPillar3aContribution(employmentType, income);
  const optimalContribution = Decimal.min(maxContribution, income.mul(0.2));

  const currentTaxSavings = currentContribution.mul(marginalTaxRate);
  const optimalTaxSavings = optimalContribution.mul(marginalTaxRate);

  return {
    current: {
      contribution: currentContribution,
      taxSavings: currentTaxSavings
    },
    optimal: {
      contribution: optimalContribution,
      taxSavings: optimalTaxSavings
    },
    improvement: {
      additionalContribution: optimalContribution.minus(currentContribution),
      additionalSavings: optimalTaxSavings.minus(currentTaxSavings)
    }
  };
}
```

### Multi-Account Withdrawal Strategy

```typescript
function calculateOptimalWithdrawalStrategy(
  accounts: Pillar3aAccount[],
  retirementAge: number,
  targetAge: number = 65
): WithdrawalStrategy {
  const withdrawalYears = targetAge - retirementAge;
  const accountsPerYear = Math.ceil(accounts.length / withdrawalYears);

  const strategy: WithdrawalPlan[] = [];

  for (let year = 0; year < withdrawalYears; year++) {
    const startIndex = year * accountsPerYear;
    const endIndex = Math.min(startIndex + accountsPerYear, accounts.length);
    const yearAccounts = accounts.slice(startIndex, endIndex);

    const totalWithdrawal = yearAccounts.reduce(
      (sum, account) => sum.plus(account.balance),
      new Decimal(0)
    );

    const estimatedTax = calculateWithdrawalTax(
      totalWithdrawal,
      retirementAge + year
    );

    strategy.push({
      year: retirementAge + year,
      accounts: yearAccounts,
      grossWithdrawal: totalWithdrawal,
      estimatedTax,
      netWithdrawal: totalWithdrawal.minus(estimatedTax)
    });
  }

  return { plans: strategy, totalTaxSavings: calculateTaxSavings(strategy) };
}
```

## Monte Carlo Simulations

### Market Return Modeling

```typescript
interface MarketScenario {
  returns: number[];
  probability: number;
  description: string;
}

function runMonteCarloSimulation(
  initialWealth: Decimal,
  monthlyContribution: Decimal,
  scenarios: MarketScenario[],
  timeHorizon: number,
  iterations: number = 10000
): MonteCarloResult {
  const results: SimulationResult[] = [];

  for (let i = 0; i < iterations; i++) {
    const scenario = selectRandomScenario(scenarios);
    const finalWealth = simulateWealthGrowth(
      initialWealth,
      monthlyContribution,
      scenario.returns,
      timeHorizon
    );

    results.push({
      finalWealth,
      scenario: scenario.description,
      success: finalWealth.gte(targetFIRENumber)
    });
  }

  return analyzeResults(results);
}

function simulateWealthGrowth(
  initialWealth: Decimal,
  monthlyContribution: Decimal,
  returns: number[],
  years: number
): Decimal {
  let wealth = initialWealth;

  for (let year = 0; year < years; year++) {
    const annualReturn = new Decimal(returns[year % returns.length]);

    // Add monthly contributions throughout the year
    for (let month = 0; month < 12; month++) {
      wealth = wealth.plus(monthlyContribution);
      const monthlyReturn = annualReturn.div(12);
      wealth = wealth.mul(monthlyReturn.plus(1));
    }
  }

  return wealth;
}
```

### Risk Assessment

```typescript
function assessPortfolioRisk(
  allocation: AssetAllocation,
  timeHorizon: number
): RiskAssessment {
  const expectedReturn = calculateExpectedReturn(allocation);
  const volatility = calculatePortfolioVolatility(allocation);

  // Value at Risk (95% confidence)
  const var95 = expectedReturn.minus(volatility.mul(1.645));

  // Conditional Value at Risk
  const cvar95 = calculateConditionalVaR(allocation, 0.05);

  // Sequence of Returns Risk
  const sequenceRisk = calculateSequenceRisk(
    expectedReturn,
    volatility,
    timeHorizon
  );

  return {
    expectedReturn,
    volatility,
    valueAtRisk: var95,
    conditionalVaR: cvar95,
    sequenceRisk,
    riskScore: calculateOverallRiskScore({
      volatility,
      sequenceRisk,
      timeHorizon
    })
  };
}
```

## Economic Data Integration

### Real-time Market Data

```typescript
interface EconomicIndicators {
  snbPolicyRate: Decimal;
  inflation: Decimal;
  swissMarketIndex: Decimal;
  bondYields: BondYield[];
  currencyRates: CurrencyRate[];
}

function adjustProjectionsForEconomicData(
  baseProjection: WealthProjection[],
  indicators: EconomicIndicators
): WealthProjection[] {
  return baseProjection.map(projection => {
    // Adjust returns based on current economic conditions
    const adjustedReturn = adjustReturnForEconomicConditions(
      projection.expectedReturn,
      indicators
    );

    // Adjust inflation expectations
    const adjustedInflation = adjustInflationExpectations(
      projection.inflationRate,
      indicators.inflation
    );

    return {
      ...projection,
      expectedReturn: adjustedReturn,
      inflationRate: adjustedInflation,
      realReturn: adjustedReturn.minus(adjustedInflation)
    };
  });
}
```

### Swiss Market Adjustments

```typescript
function applySwissMarketAdjustments(
  baseReturn: Decimal,
  indicators: EconomicIndicators
): Decimal {
  // Adjust for SNB policy rate changes
  const rateAdjustment = indicators.snbPolicyRate.minus(0.015).mul(0.3);

  // Adjust for Swiss franc strength
  const currencyAdjustment = calculateCurrencyImpact(indicators.currencyRates);

  // Adjust for domestic market conditions
  const marketAdjustment = calculateMarketConditionAdjustment(
    indicators.swissMarketIndex
  );

  return baseReturn
    .plus(rateAdjustment)
    .plus(currencyAdjustment)
    .plus(marketAdjustment);
}
```

## Performance Optimization

### Calculation Caching

```typescript
class CalculationCache {
  private cache = new Map<string, CacheEntry>();
  private readonly TTL = 5 * 60 * 1000; // 5 minutes

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry || Date.now() > entry.expiry) {
      this.cache.delete(key);
      return null;
    }
    return entry.value as T;
  }

  set<T>(key: string, value: T): void {
    this.cache.set(key, {
      value,
      expiry: Date.now() + this.TTL
    });
  }

  generateKey(params: any): string {
    return JSON.stringify(params, Object.keys(params).sort());
  }
}

// Usage in calculations
function cachedProjectWealth(params: ProjectionParams): WealthProjection[] {
  const cacheKey = cache.generateKey(params);
  const cached = cache.get<WealthProjection[]>(cacheKey);

  if (cached) return cached;

  const result = projectWealth(params);
  cache.set(cacheKey, result);
  return result;
}
```

### Batch Calculations

```typescript
function batchCalculateScenarios(
  baseParams: ProjectionParams,
  variations: Partial<ProjectionParams>[]
): ScenarioResult[] {
  // Group similar calculations for efficiency
  const grouped = groupSimilarCalculations(variations);

  return grouped.flatMap(group => {
    // Optimize calculations for similar parameters
    const baseResult = projectWealth({ ...baseParams, ...group.common });

    return group.variations.map(variation => {
      // Apply incremental changes to base result
      return applyVariationToResult(baseResult, variation);
    });
  });
}
```

## Validation and Error Handling

### Input Validation

```typescript
function validateCalculationInputs(params: any): ValidationResult {
  const errors: ValidationError[] = [];

  // Validate numerical inputs
  if (!isValidDecimal(params.income)) {
    errors.push({
      field: 'income',
      message: 'Income must be a positive number',
      code: 'INVALID_INCOME'
    });
  }

  // Validate ranges
  if (params.withdrawalRate?.gt(0.1)) {
    errors.push({
      field: 'withdrawalRate',
      message: 'Withdrawal rate should not exceed 10%',
      code: 'WITHDRAWAL_RATE_TOO_HIGH'
    });
  }

  // Validate Swiss-specific constraints
  if (params.canton && !isValidCanton(params.canton)) {
    errors.push({
      field: 'canton',
      message: 'Invalid Swiss canton code',
      code: 'INVALID_CANTON'
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
```

### Calculation Bounds

```typescript
function enforceCalculationBounds<T extends Decimal>(
  value: T,
  min: Decimal,
  max: Decimal,
  fieldName: string
): T {
  if (value.lt(min)) {
    console.warn(`${fieldName} below minimum (${min}), clamping to minimum`);
    return min as T;
  }

  if (value.gt(max)) {
    console.warn(`${fieldName} above maximum (${max}), clamping to maximum`);
    return max as T;
  }

  return value;
}
```

## Testing and Verification

### Unit Test Examples

```typescript
describe('Swiss Tax Calculations', () => {
  test('federal tax calculation accuracy', () => {
    const income = new Decimal(100000);
    const result = calculateFederalTax(income);

    // Verify against known tax tables
    expect(result.amount.toNumber()).toBeCloseTo(7834.50, 2);
    expect(result.effectiveRate.toNumber()).toBeCloseTo(0.0783, 4);
  });

  test('cantonal tax variations', () => {
    const income = new Decimal(80000);

    const zurichTax = calculateCantonalTax(income, CANTON_CONFIGS.ZH);
    const zugTax = calculateCantonalTax(income, CANTON_CONFIGS.ZG);

    expect(zugTax.total.lt(zurichTax.total)).toBe(true);
  });
});
```

---

*These advanced calculations form the mathematical foundation of Swiss Budget Pro. All formulas are validated against Swiss financial standards and continuously tested for accuracy.*
