# Performance Optimization

Swiss Budget Pro is designed for optimal performance across all devices and network conditions. This guide covers performance optimization techniques, monitoring, and best practices.

## Performance Overview

### Key Performance Metrics

```{grid} 1 2 2 2
:gutter: 3

```{grid-item-card} ⚡ Load Performance
**Target Metrics:**
- First Contentful Paint: <1.5s
- Largest Contentful Paint: <2.5s
- Time to Interactive: <3.0s
- Bundle Size: <500KB gzipped
```

```{grid-item-card} 🔄 Runtime Performance
**Target Metrics:**
- Calculation Updates: <100ms
- Chart Rendering: <200ms
- Memory Usage: <50MB
- CPU Usage: <10% average
```

```{grid-item-card} 📱 Mobile Performance
**Target Metrics:**
- Mobile Load Time: <3.0s
- Touch Response: <16ms
- Battery Impact: Minimal
- Offline Capability: Full
```

```{grid-item-card} 🌐 Network Performance
**Target Metrics:**
- 3G Load Time: <5.0s
- Offline Mode: Available
- Data Usage: <1MB/session
- Cache Hit Rate: >90%
```

## Bundle Optimization

### Code Splitting Strategy

```typescript
// Route-based code splitting
const Dashboard = lazy(() => import('./pages/Dashboard'));
const Planning = lazy(() => import('./pages/Planning'));
const Analytics = lazy(() => import('./pages/Analytics'));
const Settings = lazy(() => import('./pages/Settings'));

// Component-based code splitting
const AdvancedCharts = lazy(() => import('./components/AdvancedCharts'));
const TaxOptimizer = lazy(() => import('./components/TaxOptimizer'));

// Feature-based code splitting
const MonteCarloEngine = lazy(() => 
  import('./features/MonteCarlo').then(module => ({
    default: module.MonteCarloEngine
  }))
);

// Dynamic imports for heavy calculations
async function loadCalculationEngine() {
  const { CalculationEngine } = await import('./engines/CalculationEngine');
  return new CalculationEngine();
}
```

### Tree Shaking Optimization

```typescript
// ✅ Good: Named imports for tree shaking
import { calculateCompoundInterest, calculateTax } from './utils/calculations';
import { formatCurrency } from './utils/formatting';

// ❌ Avoid: Default imports that prevent tree shaking
import * as calculations from './utils/calculations';
import utils from './utils';

// ✅ Good: Conditional loading of heavy features
const loadAdvancedFeatures = async () => {
  if (userNeedsAdvancedFeatures) {
    const { AdvancedCalculations } = await import('./advanced/calculations');
    return AdvancedCalculations;
  }
  return null;
};

// ✅ Good: Lazy loading of D3.js modules
const loadD3Charts = async () => {
  const [d3Selection, d3Scale, d3Axis] = await Promise.all([
    import('d3-selection'),
    import('d3-scale'),
    import('d3-axis')
  ]);
  
  return { d3Selection, d3Scale, d3Axis };
};
```

### Asset Optimization

```typescript
// Image optimization with lazy loading
const OptimizedImage: React.FC<ImageProps> = ({ src, alt, ...props }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );
    
    if (imgRef.current) {
      observer.observe(imgRef.current);
    }
    
    return () => observer.disconnect();
  }, []);
  
  return (
    <img
      ref={imgRef}
      src={isInView ? src : undefined}
      alt={alt}
      onLoad={() => setIsLoaded(true)}
      style={{
        opacity: isLoaded ? 1 : 0,
        transition: 'opacity 0.3s ease'
      }}
      {...props}
    />
  );
};

// Font optimization
const fontPreload = `
  <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="/fonts/jetbrains-mono.woff2" as="font" type="font/woff2" crossorigin>
`;
```

## Runtime Performance

### Calculation Optimization

```typescript
// Memoization for expensive calculations
const useMemoizedProjections = (
  income: number,
  expenses: number,
  assets: number,
  assumptions: ProjectionAssumptions
) => {
  return useMemo(() => {
    const startTime = performance.now();
    
    const projections = calculateWealthProjections({
      income,
      expenses,
      assets,
      ...assumptions
    });
    
    const endTime = performance.now();
    console.log(`Projection calculation took ${endTime - startTime}ms`);
    
    return projections;
  }, [income, expenses, assets, assumptions]);
};

// Debounced calculations for real-time updates
const useDebouncedCalculations = (
  data: FinancialData,
  delay: number = 300
) => {
  const [debouncedData, setDebouncedData] = useState(data);
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedData(data);
    }, delay);
    
    return () => clearTimeout(timer);
  }, [data, delay]);
  
  return useMemoizedProjections(
    debouncedData.income,
    debouncedData.expenses,
    debouncedData.assets,
    debouncedData.assumptions
  );
};

// Web Workers for heavy calculations (planned)
class CalculationWorker {
  private worker: Worker | null = null;
  
  async calculateProjections(data: ProjectionData): Promise<ProjectionResult> {
    if (!this.worker) {
      this.worker = new Worker('/workers/calculations.js');
    }
    
    return new Promise((resolve, reject) => {
      this.worker!.postMessage({ type: 'CALCULATE_PROJECTIONS', data });
      
      this.worker!.onmessage = (event) => {
        if (event.data.type === 'PROJECTIONS_COMPLETE') {
          resolve(event.data.result);
        } else if (event.data.type === 'ERROR') {
          reject(new Error(event.data.message));
        }
      };
    });
  }
}
```

### React Performance Optimization

```typescript
// Component memoization
const ExpensiveChart = React.memo<ChartProps>(({ data, options }) => {
  const chartRef = useRef<SVGSVGElement>(null);
  
  useEffect(() => {
    if (!chartRef.current || !data) return;
    
    // Only re-render chart when data actually changes
    renderChart(chartRef.current, data, options);
  }, [data, options]);
  
  return <svg ref={chartRef} />;
}, (prevProps, nextProps) => {
  // Custom comparison for complex objects
  return (
    JSON.stringify(prevProps.data) === JSON.stringify(nextProps.data) &&
    JSON.stringify(prevProps.options) === JSON.stringify(nextProps.options)
  );
});

// Callback optimization
const FinancialForm: React.FC<FormProps> = ({ onUpdate }) => {
  const [formData, setFormData] = useState(initialData);
  
  // Memoize callback to prevent unnecessary re-renders
  const handleUpdate = useCallback((field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);
  
  // Debounce external updates
  const debouncedUpdate = useDebouncedCallback(onUpdate, 300);
  
  useEffect(() => {
    debouncedUpdate(formData);
  }, [formData, debouncedUpdate]);
  
  return (
    <form>
      {/* Form fields */}
    </form>
  );
};

// Virtual scrolling for large lists
const VirtualizedScenarioList: React.FC<ScenarioListProps> = ({ scenarios }) => {
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 10 });
  const containerRef = useRef<HTMLDivElement>(null);
  
  const handleScroll = useCallback(() => {
    if (!containerRef.current) return;
    
    const { scrollTop, clientHeight } = containerRef.current;
    const itemHeight = 60; // Fixed item height
    
    const start = Math.floor(scrollTop / itemHeight);
    const end = Math.min(
      start + Math.ceil(clientHeight / itemHeight) + 5,
      scenarios.length
    );
    
    setVisibleRange({ start, end });
  }, [scenarios.length]);
  
  const visibleScenarios = scenarios.slice(visibleRange.start, visibleRange.end);
  
  return (
    <div
      ref={containerRef}
      onScroll={handleScroll}
      style={{ height: '400px', overflow: 'auto' }}
    >
      <div style={{ height: scenarios.length * 60 }}>
        <div style={{ transform: `translateY(${visibleRange.start * 60}px)` }}>
          {visibleScenarios.map((scenario, index) => (
            <ScenarioItem
              key={scenario.id}
              scenario={scenario}
              index={visibleRange.start + index}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
```

## Memory Management

### Memory Leak Prevention

```typescript
// Proper cleanup of event listeners
const useEventListener = (
  eventName: string,
  handler: (event: Event) => void,
  element: EventTarget = window
) => {
  const savedHandler = useRef(handler);
  
  useEffect(() => {
    savedHandler.current = handler;
  }, [handler]);
  
  useEffect(() => {
    const eventListener = (event: Event) => savedHandler.current(event);
    element.addEventListener(eventName, eventListener);
    
    return () => {
      element.removeEventListener(eventName, eventListener);
    };
  }, [eventName, element]);
};

// Memory-efficient data structures
class CircularBuffer<T> {
  private buffer: T[];
  private head = 0;
  private tail = 0;
  private size = 0;
  
  constructor(private capacity: number) {
    this.buffer = new Array(capacity);
  }
  
  push(item: T): void {
    this.buffer[this.tail] = item;
    this.tail = (this.tail + 1) % this.capacity;
    
    if (this.size < this.capacity) {
      this.size++;
    } else {
      this.head = (this.head + 1) % this.capacity;
    }
  }
  
  toArray(): T[] {
    const result: T[] = [];
    for (let i = 0; i < this.size; i++) {
      const index = (this.head + i) % this.capacity;
      result.push(this.buffer[index]);
    }
    return result;
  }
}

// Efficient data cleanup
class DataManager {
  private cache = new Map<string, CacheEntry>();
  private cleanupTimer: NodeJS.Timeout | null = null;
  
  constructor() {
    this.startCleanupTimer();
  }
  
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredEntries();
    }, 60000); // Cleanup every minute
  }
  
  private cleanupExpiredEntries(): void {
    const now = Date.now();
    let cleaned = 0;
    
    for (const [key, entry] of this.cache) {
      if (now > entry.expiry) {
        this.cache.delete(key);
        cleaned++;
      }
    }
    
    if (cleaned > 0) {
      console.log(`Cleaned up ${cleaned} expired cache entries`);
    }
  }
  
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.cache.clear();
  }
}
```

### Garbage Collection Optimization

```typescript
// Object pooling for frequently created objects
class ObjectPool<T> {
  private pool: T[] = [];
  private createFn: () => T;
  private resetFn: (obj: T) => void;
  
  constructor(createFn: () => T, resetFn: (obj: T) => void, initialSize = 10) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    
    // Pre-populate pool
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(this.createFn());
    }
  }
  
  acquire(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    return this.createFn();
  }
  
  release(obj: T): void {
    this.resetFn(obj);
    this.pool.push(obj);
  }
}

// Example usage for calculation objects
const calculationPool = new ObjectPool(
  () => ({ income: 0, expenses: 0, result: 0 }),
  (obj) => { obj.income = 0; obj.expenses = 0; obj.result = 0; },
  50
);

function performCalculation(income: number, expenses: number): number {
  const calc = calculationPool.acquire();
  calc.income = income;
  calc.expenses = expenses;
  calc.result = income - expenses;
  
  const result = calc.result;
  calculationPool.release(calc);
  
  return result;
}
```

## Caching Strategies

### Multi-Level Caching

```typescript
interface CacheLevel {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
}

class MemoryCache implements CacheLevel {
  private cache = new Map<string, { value: any; expiry: number }>();
  
  async get<T>(key: string): Promise<T | null> {
    const entry = this.cache.get(key);
    if (!entry || Date.now() > entry.expiry) {
      this.cache.delete(key);
      return null;
    }
    return entry.value as T;
  }
  
  async set<T>(key: string, value: T, ttl = 300000): Promise<void> {
    this.cache.set(key, {
      value,
      expiry: Date.now() + ttl
    });
  }
}

class LocalStorageCache implements CacheLevel {
  private prefix = 'cache:';
  
  async get<T>(key: string): Promise<T | null> {
    try {
      const item = localStorage.getItem(this.prefix + key);
      if (!item) return null;
      
      const parsed = JSON.parse(item);
      if (Date.now() > parsed.expiry) {
        localStorage.removeItem(this.prefix + key);
        return null;
      }
      
      return parsed.value as T;
    } catch {
      return null;
    }
  }
  
  async set<T>(key: string, value: T, ttl = 3600000): Promise<void> {
    const item = {
      value,
      expiry: Date.now() + ttl
    };
    localStorage.setItem(this.prefix + key, JSON.stringify(item));
  }
}

class MultiLevelCache {
  constructor(
    private l1: MemoryCache,
    private l2: LocalStorageCache
  ) {}
  
  async get<T>(key: string): Promise<T | null> {
    // Try L1 cache first
    let value = await this.l1.get<T>(key);
    if (value !== null) return value;
    
    // Try L2 cache
    value = await this.l2.get<T>(key);
    if (value !== null) {
      // Promote to L1 cache
      await this.l1.set(key, value);
      return value;
    }
    
    return null;
  }
  
  async set<T>(key: string, value: T): Promise<void> {
    await Promise.all([
      this.l1.set(key, value),
      this.l2.set(key, value)
    ]);
  }
}
```

## Performance Monitoring

### Real-Time Performance Tracking

```typescript
class PerformanceMonitor {
  private metrics = new Map<string, PerformanceMetric[]>();
  private observers: PerformanceObserver[] = [];
  
  constructor() {
    this.setupObservers();
  }
  
  private setupObservers(): void {
    // Monitor navigation timing
    const navObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        this.recordMetric('navigation', {
          name: entry.name,
          duration: entry.duration,
          startTime: entry.startTime
        });
      }
    });
    navObserver.observe({ entryTypes: ['navigation'] });
    this.observers.push(navObserver);
    
    // Monitor resource loading
    const resourceObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        this.recordMetric('resource', {
          name: entry.name,
          duration: entry.duration,
          transferSize: (entry as PerformanceResourceTiming).transferSize
        });
      }
    });
    resourceObserver.observe({ entryTypes: ['resource'] });
    this.observers.push(resourceObserver);
    
    // Monitor long tasks
    const longTaskObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        this.recordMetric('longtask', {
          name: 'long-task',
          duration: entry.duration,
          startTime: entry.startTime
        });
      }
    });
    longTaskObserver.observe({ entryTypes: ['longtask'] });
    this.observers.push(longTaskObserver);
  }
  
  recordMetric(category: string, metric: PerformanceMetric): void {
    if (!this.metrics.has(category)) {
      this.metrics.set(category, []);
    }
    
    const categoryMetrics = this.metrics.get(category)!;
    categoryMetrics.push(metric);
    
    // Keep only last 100 metrics per category
    if (categoryMetrics.length > 100) {
      categoryMetrics.shift();
    }
  }
  
  getMetrics(category: string): PerformanceMetric[] {
    return this.metrics.get(category) || [];
  }
  
  getAverageMetric(category: string, field: keyof PerformanceMetric): number {
    const metrics = this.getMetrics(category);
    if (metrics.length === 0) return 0;
    
    const sum = metrics.reduce((acc, metric) => {
      const value = metric[field];
      return acc + (typeof value === 'number' ? value : 0);
    }, 0);
    
    return sum / metrics.length;
  }
}

// Usage in React components
const usePerformanceTracking = (componentName: string) => {
  const renderStart = useRef<number>(0);
  
  useEffect(() => {
    renderStart.current = performance.now();
  });
  
  useLayoutEffect(() => {
    const renderTime = performance.now() - renderStart.current;
    performanceMonitor.recordMetric('component-render', {
      name: componentName,
      duration: renderTime,
      startTime: renderStart.current
    });
  });
};
```

### Performance Budgets

```typescript
interface PerformanceBudget {
  category: string;
  metric: string;
  threshold: number;
  unit: 'ms' | 'kb' | 'count';
}

const performanceBudgets: PerformanceBudget[] = [
  { category: 'bundle', metric: 'size', threshold: 500, unit: 'kb' },
  { category: 'calculation', metric: 'duration', threshold: 100, unit: 'ms' },
  { category: 'chart-render', metric: 'duration', threshold: 200, unit: 'ms' },
  { category: 'memory', metric: 'usage', threshold: 50, unit: 'kb' },
];

class PerformanceBudgetMonitor {
  checkBudgets(): BudgetViolation[] {
    const violations: BudgetViolation[] = [];
    
    for (const budget of performanceBudgets) {
      const currentValue = this.getCurrentValue(budget);
      
      if (currentValue > budget.threshold) {
        violations.push({
          budget,
          currentValue,
          violation: currentValue - budget.threshold
        });
      }
    }
    
    return violations;
  }
  
  private getCurrentValue(budget: PerformanceBudget): number {
    switch (budget.category) {
      case 'bundle':
        return this.getBundleSize();
      case 'calculation':
        return performanceMonitor.getAverageMetric('calculation', 'duration');
      case 'chart-render':
        return performanceMonitor.getAverageMetric('chart-render', 'duration');
      case 'memory':
        return this.getMemoryUsage();
      default:
        return 0;
    }
  }
}
```

## Mobile Optimization

### Touch Performance

```typescript
// Optimized touch handling
const useTouchOptimization = () => {
  useEffect(() => {
    // Prevent default touch behaviors that can cause delays
    const preventDefaults = (e: TouchEvent) => {
      // Only prevent if not a scrollable area
      if (!e.target?.closest('.scrollable')) {
        e.preventDefault();
      }
    };
    
    document.addEventListener('touchstart', preventDefaults, { passive: false });
    document.addEventListener('touchmove', preventDefaults, { passive: false });
    
    return () => {
      document.removeEventListener('touchstart', preventDefaults);
      document.removeEventListener('touchmove', preventDefaults);
    };
  }, []);
};

// Fast click implementation
const useFastClick = (callback: () => void) => {
  const ref = useRef<HTMLElement>(null);
  
  useEffect(() => {
    const element = ref.current;
    if (!element) return;
    
    let touchStartTime = 0;
    let touchStartX = 0;
    let touchStartY = 0;
    
    const handleTouchStart = (e: TouchEvent) => {
      touchStartTime = Date.now();
      touchStartX = e.touches[0].clientX;
      touchStartY = e.touches[0].clientY;
    };
    
    const handleTouchEnd = (e: TouchEvent) => {
      const touchEndTime = Date.now();
      const touchEndX = e.changedTouches[0].clientX;
      const touchEndY = e.changedTouches[0].clientY;
      
      const timeDiff = touchEndTime - touchStartTime;
      const distanceX = Math.abs(touchEndX - touchStartX);
      const distanceY = Math.abs(touchEndY - touchStartY);
      
      // Fast tap detection
      if (timeDiff < 200 && distanceX < 10 && distanceY < 10) {
        e.preventDefault();
        callback();
      }
    };
    
    element.addEventListener('touchstart', handleTouchStart);
    element.addEventListener('touchend', handleTouchEnd);
    
    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }, [callback]);
  
  return ref;
};
```

---

*Performance optimization ensures Swiss Budget Pro delivers a fast, responsive experience across all devices and network conditions.*
