# Component Architecture

This document details the component structure and organization of Swiss Budget Pro, explaining how the React-based frontend is architected for maintainability, reusability, and Swiss-specific functionality.

## Component Hierarchy

```{mermaid}
graph TD
    A[App] --> B[SwissBudgetPro]
    B --> C[Header]
    B --> D[TabNavigation]
    B --> E[MainContent]
    B --> F[Footer]
    
    C --> C1[Logo]
    C --> C2[ThemeToggle]
    C --> C3[ExportButton]
    
    D --> D1[InputTab]
    D --> D2[AnalysisTab]
    D --> D3[VisualizationTab]
    D --> D4[ReportsTab]
    
    E --> E1[InputForms]
    E --> E2[CalculationResults]
    E --> E3[Charts]
    E --> E4[ReportGenerator]
    
    E1 --> F1[IncomeForm]
    E1 --> F2[ExpenseForm]
    E1 --> F3[SavingsForm]
    E1 --> F4[SwissConfigForm]
    
    E2 --> G1[BasicCalculations]
    E2 --> G2[MonteCarloResults]
    E2 --> G3[TaxAnalysis]
    E2 --> G4[FIREProgress]
    
    E3 --> H1[BudgetDonutChart]
    E3 --> H2[ProjectionLineChart]
    E3 --> H3[HistoricalTrends]
    E3 --> H4[RiskAnalysisChart]
    
    E4 --> I1[PDFGenerator]
    E4 --> I2[CSVExporter]
    E4 --> I3[RecommendationEngine]
```

## Core Components

### App Component
**Purpose**: Root application component that provides global context and routing.

**Responsibilities**:
- Initialize application state
- Provide theme context (dark/light mode)
- Handle global error boundaries
- Manage localStorage integration
- Set up performance monitoring

**Key Features**:
- Error boundary for graceful failure handling
- Theme persistence across sessions
- Global keyboard shortcuts
- Accessibility features (ARIA labels, focus management)

### SwissBudgetPro Component
**Purpose**: Main application container that orchestrates all financial planning functionality.

**Responsibilities**:
- Coordinate data flow between input and calculation components
- Manage application state with React Context
- Handle auto-save functionality
- Orchestrate real-time calculations
- Manage tab navigation and active views

**State Management**:
```typescript
interface SwissBudgetState {
  personalInfo: PersonalInfo;
  income: IncomeData;
  expenses: ExpenseData;
  savings: SavingsData;
  swissConfig: SwissConfiguration;
  calculations: CalculationResults;
  preferences: UserPreferences;
}
```

## Input Components

### IncomeForm Component
**Purpose**: Capture and validate all income sources with Swiss-specific features.

**Features**:
- **Primary Employment**: Salary with work time percentage
- **Company Income**: Business revenue with growth projections
- **Contract Work**: HSLU, RUAG, and other contract income
- **Investment Income**: Dividends, interest, rental income
- **Swiss Validation**: CHF formatting, reasonable income ranges

**Validation Rules**:
- Income ranges: CHF 0 - 1,000,000 annually
- Work time percentage: 10% - 100%
- Growth rates: -50% to +100% annually
- Contract duration: 1 month to 10 years

### ExpenseForm Component
**Purpose**: Comprehensive expense tracking with Swiss cost-of-living considerations.

**Categories**:
- **Essential Expenses**: Housing, food, transportation, insurance
- **Discretionary Expenses**: Entertainment, travel, hobbies
- **Swiss-Specific**: Health insurance (mandatory), cantonal taxes
- **Dynamic Categories**: User-defined expense categories

**Features**:
- Real-time budget allocation visualization
- Swiss average cost suggestions
- Expense trend analysis
- Category-based budgeting tools

### SavingsForm Component
**Purpose**: Swiss retirement and investment planning with regulatory compliance.

**Savings Types**:
- **Emergency Fund**: 3-12 months of expenses
- **Pillar 3a**: CHF 7,056 annual limit (2024)
- **Investment Portfolio**: Stocks, bonds, ETFs
- **Pension (BVG)**: Leaving benefits and projections

**Swiss Features**:
- Automatic Pillar 3a limit enforcement
- Tax-optimized contribution timing
- BVG pension integration
- Cantonal tax impact calculations

### SwissConfigForm Component
**Purpose**: Swiss-specific configuration for accurate tax and regulatory calculations.

**Configuration Options**:
- **Canton Selection**: All 26 Swiss cantons with tax rates
- **Civil Status**: Single, married, divorced, widowed
- **Employment Type**: Employee, self-employed, mixed
- **Tax Optimization**: Preferences for tax-saving strategies

**Advanced Features**:
- Cantonal comparison tool
- Tax burden visualization
- Relocation impact analysis
- Wealth tax considerations

## Calculation Components

### BasicCalculations Component
**Purpose**: Display fundamental financial metrics and projections.

**Metrics Displayed**:
- Monthly cash flow and savings rate
- Net worth progression over time
- FIRE number and timeline
- Real vs nominal value analysis

**Features**:
- Real-time updates as inputs change
- Historical comparison with previous calculations
- Milestone tracking and notifications
- Sensitivity analysis for key variables

### MonteCarloResults Component
**Purpose**: Advanced risk analysis with thousands of market simulations.

**Analysis Features**:
- **Stress Testing**: 5 economic scenarios
- **Success Probability**: Likelihood of reaching FIRE goals
- **Risk Metrics**: Value at Risk, expected shortfall
- **Safe Withdrawal Rate**: Optimized retirement income

**Visualization**:
- Probability distribution charts
- Scenario comparison tables
- Risk tolerance assessment
- Confidence interval displays

### TaxAnalysis Component
**Purpose**: Comprehensive Swiss tax optimization and planning.

**Tax Features**:
- **Current Tax Calculation**: Federal and cantonal taxes
- **Cantonal Comparison**: Tax burden across all cantons
- **Pillar 3a Optimization**: Multi-account strategies
- **Wealth Tax Planning**: High net worth considerations

**Optimization Tools**:
- Tax-saving recommendations
- Optimal contribution timing
- Relocation tax impact
- Estate planning considerations

### FIREProgress Component
**Purpose**: Track progress toward Financial Independence and Early Retirement.

**Progress Metrics**:
- Current FIRE percentage
- Years remaining to FIRE
- Monthly progress tracking
- Milestone celebrations

**Features**:
- Visual progress bars and charts
- Goal adjustment tools
- Scenario impact analysis
- Motivation and gamification elements

## Visualization Components

### BudgetDonutChart Component
**Purpose**: Interactive expense allocation visualization using D3.js.

**Features**:
- **Interactive Segments**: Hover for detailed information
- **Swiss Formatting**: CHF currency with proper formatting
- **Category Breakdown**: Essential vs discretionary expenses
- **Responsive Design**: Adapts to different screen sizes

**Technical Implementation**:
- D3.js for smooth animations and interactions
- TypeScript interfaces for type-safe data handling
- Accessibility features for screen readers
- Mobile-optimized touch interactions

### ProjectionLineChart Component
**Purpose**: Long-term financial trajectory visualization with multiple data series.

**Data Series**:
- Total net worth progression
- Investment portfolio growth
- Pension and Pillar 3a accumulation
- Real vs nominal value comparison

**Interactive Features**:
- Zoom and pan functionality
- Hover tooltips with detailed information
- Scenario comparison overlays
- Export to image functionality

### HistoricalTrends Component
**Purpose**: Track financial progress over time with trend analysis.

**Trend Analysis**:
- Monthly savings rate trends
- Net worth growth patterns
- Goal achievement tracking
- Performance benchmarking

**Features**:
- Customizable time ranges
- Trend line calculations
- Anomaly detection and highlighting
- Comparison with Swiss averages

### RiskAnalysisChart Component
**Purpose**: Visualize portfolio risk and Monte Carlo simulation results.

**Risk Visualizations**:
- Probability distribution curves
- Confidence interval bands
- Scenario outcome charts
- Risk-return scatter plots

**Interactive Elements**:
- Risk tolerance sliders
- Scenario selection tools
- Stress test visualizations
- Performance attribution analysis

## Utility Components

### ThemeToggle Component
**Purpose**: Switch between light and dark themes with persistence.

**Features**:
- Smooth theme transitions
- System preference detection
- localStorage persistence
- Accessibility compliance

### ExportButton Component
**Purpose**: Generate and download financial reports in various formats.

**Export Formats**:
- PDF reports with Swiss formatting
- CSV data for external analysis
- JSON backup files
- Image exports of charts

### LoadingSpinner Component
**Purpose**: Provide user feedback during calculations and data loading.

**Features**:
- Swiss-themed loading animations
- Progress indicators for long calculations
- Accessible loading states
- Graceful error handling

## Custom Hooks

### useLocalStorage Hook
**Purpose**: Manage localStorage with automatic serialization and error handling.

**Features**:
- Automatic JSON serialization
- Error recovery for corrupted data
- Debounced saves for performance
- TypeScript type safety

### useCalculations Hook
**Purpose**: Manage complex financial calculations with memoization.

**Features**:
- Memoized expensive calculations
- Dependency tracking for updates
- Error handling and fallbacks
- Performance monitoring

### useSwissTax Hook
**Purpose**: Handle Swiss tax calculations and optimization.

**Features**:
- Canton-specific tax calculations
- Pillar 3a optimization algorithms
- Tax-saving recommendations
- Real-time tax impact analysis

## State Management

### Context Providers
Swiss Budget Pro uses React Context for global state management:

```typescript
// Main application context
const SwissBudgetContext = createContext<SwissBudgetState>();

// Theme context for dark/light mode
const ThemeContext = createContext<ThemeState>();

// Calculation context for financial results
const CalculationContext = createContext<CalculationState>();
```

### State Updates
- **Immutable Updates**: All state changes create new objects
- **Batched Updates**: Multiple changes batched for performance
- **Validation**: All updates validated before applying
- **History**: Undo/redo functionality for user convenience

## Performance Optimization

### Component Optimization
- **React.memo**: Prevent unnecessary re-renders
- **useMemo**: Memoize expensive calculations
- **useCallback**: Stable function references
- **Code Splitting**: Lazy load heavy components

### Calculation Optimization
- **Web Workers**: Move heavy calculations to background
- **Debouncing**: Batch rapid input changes
- **Caching**: Cache calculation results
- **Progressive Loading**: Load features on demand

## Testing Strategy

### Component Testing
- **Unit Tests**: Individual component functionality
- **Integration Tests**: Component interaction testing
- **Visual Tests**: Screenshot comparison testing
- **Accessibility Tests**: ARIA compliance and keyboard navigation

### Test Scenarios
- **Swiss Financial Scenarios**: Young professional, family, near-retirement
- **Edge Cases**: Zero income, maximum values, corrupted data
- **User Interactions**: Form submissions, chart interactions, exports
- **Error Conditions**: Network failures, calculation errors, storage issues

## Future Enhancements

### Component Improvements
- **Micro-frontends**: Modular architecture for large teams
- **Component Library**: Reusable Swiss financial components
- **Advanced Visualizations**: 3D charts and VR interfaces
- **Real-time Collaboration**: Multi-user financial planning

### Technical Upgrades
- **React 19**: Latest React features and optimizations
- **Web Components**: Framework-agnostic component library
- **Progressive Web App**: Native app-like experience
- **Advanced Caching**: Service worker integration

---

This component architecture ensures Swiss Budget Pro remains maintainable, performant, and extensible while providing the best possible user experience for Swiss financial planning.
