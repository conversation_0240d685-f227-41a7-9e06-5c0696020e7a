# Data Flow Architecture

This document details how data flows through Swiss Budget Pro's architecture, from user input to final visualization and recommendations.

## Overview

Swiss Budget Pro uses a reactive data flow architecture where changes in any input immediately trigger recalculations throughout the system. This ensures users always see up-to-date results and recommendations.

## Data Flow Stages

### 1. Input Collection & Validation

```{mermaid}
graph LR
    A[User Input] --> B[Validation Layer]
    B --> C{Valid?}
    C -->|Yes| D[State Update]
    C -->|No| E[Error Display]
    D --> F[Trigger Calculations]
    E --> A
```

**Input Sources:**
- **Personal Information**: Age, retirement age, location preferences
- **Income Data**: Salary, business income, contract work, growth rates
- **Expense Data**: Essential and discretionary spending categories
- **Savings Goals**: Emergency fund, Pillar 3a, investments, pension
- **Swiss Configuration**: Canton, civil status, employment type

**Validation Rules:**
- **Type Checking**: Ensure numeric inputs are valid numbers
- **Range Validation**: Reasonable limits (e.g., age 18-100, income 0-1M CHF)
- **Swiss-Specific Rules**: Pillar 3a limits, cantonal validation
- **Dependency Checks**: Retirement age > current age, etc.

### 2. Core Calculation Engine Processing

```{mermaid}
graph TD
    A[Validated Inputs] --> B[Core Calculation Engine]
    B --> C[Basic Financial Calculations]
    B --> D[Timeline Generation]
    B --> E[Compound Interest Modeling]
    
    C --> F[Monthly Cash Flow]
    C --> G[Annual Savings Rate]
    C --> H[Net Worth Progression]
    
    D --> I[Year-by-Year Projections]
    E --> J[Investment Growth]
    E --> K[Inflation Adjustments]
    
    F --> L[Advanced Engines]
    G --> L
    H --> L
    I --> L
    J --> L
    K --> L
```

**Core Calculations:**
- **Cash Flow Analysis**: Monthly income minus expenses
- **Savings Rate**: Percentage of income saved
- **Net Worth Tracking**: Assets minus liabilities over time
- **Compound Growth**: Investment returns with reinvestment
- **Inflation Impact**: Real vs nominal value calculations

### 3. Advanced Engine Processing

The core calculations feed into four specialized engines that run in parallel:

#### Monte Carlo Engine Data Flow

```{mermaid}
graph LR
    A[Core Results] --> B[Monte Carlo Engine]
    B --> C[Scenario Generation]
    C --> D[Market Simulations]
    D --> E[Risk Metrics]
    E --> F[Success Probabilities]
```

**Processing Steps:**
1. **Scenario Setup**: Define economic scenarios (bull, bear, recession, etc.)
2. **Random Generation**: Create thousands of market return sequences
3. **Portfolio Simulation**: Apply returns to investment portfolio
4. **Outcome Analysis**: Calculate success/failure rates
5. **Risk Metrics**: VaR, expected shortfall, probability of ruin

#### Swiss Tax Engine Data Flow

```{mermaid}
graph LR
    A[Income & Location] --> B[Tax Engine]
    B --> C[Canton Tax Lookup]
    C --> D[Federal Tax Calculation]
    D --> E[Pillar 3a Optimization]
    E --> F[Tax Strategies]
```

**Processing Steps:**
1. **Canton Identification**: Load specific cantonal tax rates
2. **Income Classification**: Separate employment, business, investment income
3. **Deduction Calculation**: Standard and itemized deductions
4. **Pillar 3a Analysis**: Optimal contribution and withdrawal strategies
5. **Optimization Suggestions**: Tax-saving recommendations

#### Economic Data Engine Data Flow

```{mermaid}
graph LR
    A[Market APIs] --> B[Data Fetching]
    B --> C[Data Validation]
    C --> D[Economic Indicators]
    D --> E[Return Adjustments]
    E --> F[Alert Generation]
```

**Processing Steps:**
1. **API Calls**: Fetch data from SNB, SIX, and other sources
2. **Data Cleaning**: Validate and normalize economic data
3. **Indicator Calculation**: Derive key economic metrics
4. **Model Adjustment**: Update return assumptions based on current conditions
5. **Alert System**: Notify users of significant economic changes

#### FIRE Calculator Data Flow

```{mermaid}
graph LR
    A[Expenses & Savings] --> B[FIRE Calculator]
    B --> C[FIRE Number Calculation]
    C --> D[Timeline Projection]
    D --> E[Progress Tracking]
    E --> F[Milestone Alerts]
```

**Processing Steps:**
1. **FIRE Number**: Calculate 25x annual expenses target
2. **Current Progress**: Assess current position toward FIRE
3. **Timeline Modeling**: Project years to financial independence
4. **Scenario Analysis**: Impact of changes on FIRE timeline
5. **Milestone Tracking**: Progress notifications and celebrations

### 4. Data Persistence Layer

```{mermaid}
graph TD
    A[Calculation Results] --> B[Data Management Layer]
    B --> C[Auto-Save Trigger]
    C --> D[LocalStorage Update]
    D --> E[Historical Snapshot]
    E --> F[Backup Creation]
    
    B --> G[Export Preparation]
    G --> H[CSV Generation]
    G --> I[JSON Serialization]
    G --> J[PDF Report Creation]
```

**Persistence Strategy:**
- **Auto-Save**: Every 30 seconds or on significant changes
- **Historical Tracking**: Monthly snapshots for trend analysis
- **Multiple Scenarios**: Save different planning strategies
- **Export Formats**: CSV, JSON, and PDF for external use
- **Import Capability**: Restore from backups or migrate data

### 5. Visualization Update Pipeline

```{mermaid}
graph LR
    A[Calculation Results] --> B[Visualization Engine]
    B --> C[Data Transformation]
    C --> D[Chart Updates]
    D --> E[Interactive Features]
    E --> F[User Interface]
```

**Visualization Flow:**
1. **Data Preparation**: Transform calculation results for charts
2. **D3.js Processing**: Create interactive visualizations
3. **Chart Updates**: Smooth transitions and animations
4. **Interactivity**: Hover effects, zoom, and filtering
5. **Responsive Design**: Adapt to different screen sizes

### 6. Smart Features & Recommendations

```{mermaid}
graph TD
    A[All Engine Results] --> B[Smart Analysis Engine]
    B --> C[Pattern Recognition]
    C --> D[Optimization Opportunities]
    D --> E[Recommendation Generation]
    E --> F[Priority Ranking]
    F --> G[User Notifications]
```

**Smart Analysis:**
- **Surplus Detection**: Identify excess cash for optimization
- **Tax Opportunities**: Find tax-saving strategies
- **Risk Assessment**: Evaluate portfolio risk levels
- **Goal Tracking**: Monitor progress toward financial targets
- **Alert System**: Notify of important changes or opportunities

## Data Synchronization

### Real-time Updates
- **Reactive Architecture**: Changes propagate automatically
- **Debounced Calculations**: Batch rapid changes to improve performance
- **Priority Queue**: Critical calculations processed first
- **Error Recovery**: Graceful handling of calculation failures

### Cross-Component Communication
- **Event System**: Components communicate through events
- **State Management**: Centralized state with React Context
- **Data Validation**: Consistent validation across all components
- **Error Boundaries**: Isolate failures to prevent system crashes

## Performance Optimization

### Calculation Efficiency
- **Memoization**: Cache expensive calculations
- **Incremental Updates**: Only recalculate changed portions
- **Web Workers**: Move heavy calculations to background threads
- **Lazy Loading**: Load complex features on demand

### Memory Management
- **Garbage Collection**: Proper cleanup of large arrays
- **Data Structures**: Efficient structures for large datasets
- **Resource Monitoring**: Track memory usage and performance
- **Optimization Alerts**: Notify developers of performance issues

## Error Handling & Recovery

### Input Validation Errors
- **User-Friendly Messages**: Clear explanation of validation failures
- **Inline Feedback**: Real-time validation as user types
- **Suggestion System**: Propose valid alternatives
- **Progressive Disclosure**: Show advanced validation only when needed

### Calculation Errors
- **Fallback Values**: Use reasonable defaults when calculations fail
- **Error Logging**: Track errors for debugging and improvement
- **User Notification**: Inform users of calculation limitations
- **Recovery Strategies**: Automatic retry with simplified parameters

### Data Persistence Errors
- **LocalStorage Fallbacks**: Handle quota exceeded and corruption
- **Backup Strategies**: Multiple save locations and formats
- **Recovery Tools**: Help users restore lost data
- **Migration Support**: Handle data format changes gracefully

## Security Considerations

### Data Protection
- **Client-Side Only**: All processing happens locally
- **No Network Transmission**: Financial data never leaves the device
- **Encryption**: Sensitive data encrypted in localStorage
- **Access Control**: Secure access to stored data

### Input Sanitization
- **XSS Prevention**: Sanitize all user inputs
- **Type Safety**: TypeScript prevents type-related vulnerabilities
- **Range Validation**: Prevent overflow and underflow attacks
- **Rate Limiting**: Prevent excessive calculation requests

## Future Enhancements

### Advanced Data Flow
- **Machine Learning**: Predictive modeling for market conditions
- **Real-time Collaboration**: Multi-user financial planning
- **Advanced Caching**: Intelligent caching strategies
- **Offline Capability**: Full functionality without internet

### Integration Opportunities
- **Bank APIs**: Direct account integration (with user consent)
- **Government APIs**: Real-time tax rate updates
- **Market Data**: Enhanced real-time market integration
- **Third-party Tools**: Integration with other financial tools

---

This data flow architecture ensures Swiss Budget Pro provides accurate, real-time financial analysis while maintaining user privacy and system performance.
