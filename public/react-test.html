<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Test</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</head>
<body>
    <div id="root"></div>
    
    <script type="text/babel">
        const { useState } = React;
        
        function TestApp() {
            const [count, setCount] = useState(0);
            
            return (
                <div style={{ padding: '20px', backgroundColor: 'lightcoral' }}>
                    <h1>React Test Component</h1>
                    <p>If you can see this, React is working!</p>
                    <button onClick={() => setCount(count + 1)}>
                        Count: {count}
                    </button>
                </div>
            );
        }
        
        const container = document.getElementById('root');
        const root = ReactDOM.createRoot(container);
        root.render(<TestApp />);
    </script>
</body>
</html>
