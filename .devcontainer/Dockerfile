# Swiss Budget Pro - Development Container
FROM mcr.microsoft.com/devcontainers/typescript-node:1-20-bullseye

# Set the working directory
WORKDIR /workspaces/swiss-budget-pro

# Install additional system packages including Playwright dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    vim \
    nano \
    htop \
    tree \
    jq \
    unzip \
    build-essential \
    python3 \
    python3-pip \
    # Playwright system dependencies
    libnss3 \
    libnspr4 \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libatspi2.0-0 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libasound2 \
    libpangocairo-1.0-0 \
    libgtk-3-0 \
    libgdk-pixbuf2.0-0 \
    libxinerama1 \
    libxcursor1 \
    libxi6 \
    libxtst6 \
    libdrm2 \
    libxkbcommon0 \
    libgconf-2-4 \
    xvfb \
    fonts-liberation \
    fonts-noto-color-emoji \
    && rm -rf /var/lib/apt/lists/*

# Install global npm packages
RUN npm install -g \
    typescript \
    ts-node \
    prettier \
    eslint \
    serve \
    http-server \
    @vitejs/create-vite

# Create directories for npm global packages
RUN mkdir -p /home/<USER>/.npm-global && \
    chown -R node:node /home/<USER>/.npm-global

# Switch to node user
USER node

# Set npm global directory
RUN npm config set prefix /home/<USER>/.npm-global

# Add npm global bin to PATH
ENV PATH="/home/<USER>/.npm-global/bin:${PATH}"

# Set environment variables
ENV NODE_ENV=development
ENV TERM=xterm-256color

# Create workspace directory
RUN mkdir -p /workspaces/swiss-budget-pro

# Set the default command
CMD ["sleep", "infinity"]
