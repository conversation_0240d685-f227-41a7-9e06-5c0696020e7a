/**
 * Healthcare Cost Optimizer Component
 * Main interface for Swiss healthcare cost optimization
 */

import React, { useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import {
  UserHealthProfile,
  InsuranceRecommendation,
  DeductibleOptimization,
  healthcareOptimizer,
  AVAILABLE_DEDUCTIBLES,
  SWISS_HEALTH_INSURERS,
} from "../utils/healthcare-calculations";
import {
  HealthcareFIREAnalysis,
  healthcareFIREIntegration,
  FIREGoals,
} from "../utils/healthcare-fire-integration";
import { CantonCode } from "../utils/swiss-tax-calculations";
import {
  SWISS_PREMIUM_DATA_2024,
  getPremiumData,
} from "../data/swiss-health-insurance-data";

interface HealthcareCostOptimizerProps {
  darkMode?: boolean;
  onSavingsCalculated?: (savings: number) => void;
  fireGoals?: FIREGoals;
}

interface HealthProfileFormData {
  age: number;
  canton: CantonCode;
  income: number;
  healthStatus: "excellent" | "good" | "fair" | "poor";
  familySize: number;
  hasChildren: boolean;
  currentDeductible: number;
  currentPremium: number;
  expectedMedicalExpenses: number;
  riskTolerance: "low" | "medium" | "high";
}

const HealthcareCostOptimizer: React.FC<HealthcareCostOptimizerProps> = ({
  darkMode = false,
  onSavingsCalculated,
  fireGoals,
}) => {
  const { t } = useTranslation("healthcare");

  // State management
  const [activeTab, setActiveTab] = useState<
    "profile" | "optimization" | "fire" | "recommendations"
  >("profile");
  const [healthProfile, setHealthProfile] = useState<HealthProfileFormData>({
    age: 35,
    canton: "ZH" as CantonCode,
    income: 80000,
    healthStatus: "good",
    familySize: 1,
    hasChildren: false,
    currentDeductible: 1000,
    currentPremium: 350,
    expectedMedicalExpenses: 1200,
    riskTolerance: "medium",
  });

  const [recommendations, setRecommendations] = useState<
    InsuranceRecommendation[]
  >([]);
  const [deductibleOptimization, setDeductibleOptimization] =
    useState<DeductibleOptimization | null>(null);
  const [fireAnalysis, setFireAnalysis] =
    useState<HealthcareFIREAnalysis | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [totalSavings, setTotalSavings] = useState(0);

  // Get real premium data for user's canton and age group
  const premiumData = useMemo(() => {
    const ageGroup =
      healthProfile.age <= 18
        ? ("0-18" as const)
        : healthProfile.age <= 25
        ? ("19-25" as const)
        : ("26+" as const);

    return getPremiumData(healthProfile.canton, ageGroup);
  }, [healthProfile.canton, healthProfile.age]);

  // Calculate optimizations when profile changes
  useEffect(() => {
    if (healthProfile.age && healthProfile.canton) {
      calculateOptimizations();
    }
  }, [healthProfile, premiumData]);

  const calculateOptimizations = async () => {
    setIsCalculating(true);

    try {
      // Convert form data to health profile
      const userProfile: UserHealthProfile = {
        age: healthProfile.age,
        canton: healthProfile.canton,
        income: healthProfile.income,
        healthStatus: healthProfile.healthStatus,
        chronicConditions: [],
        historicalExpenses: [healthProfile.expectedMedicalExpenses],
        riskFactors: [],
        familySize: healthProfile.familySize,
        hasChildren: healthProfile.hasChildren,
        expectedMedicalExpenses: healthProfile.expectedMedicalExpenses,
        riskTolerance: healthProfile.riskTolerance,
      };

      // Calculate deductible optimization
      const deductibleOpt = healthcareOptimizer.optimizeDeductible(
        userProfile,
        premiumData
      );
      setDeductibleOptimization(deductibleOpt);

      // Generate insurance recommendations
      const insuranceRecs =
        healthcareOptimizer.generateInsuranceRecommendations(
          userProfile,
          premiumData
        );
      setRecommendations(insuranceRecs);

      // Calculate FIRE integration if goals provided
      if (fireGoals) {
        const currentCosts = {
          annualPremiums: healthProfile.currentPremium * 12,
          outOfPocketExpenses: healthProfile.expectedMedicalExpenses,
          supplementaryInsurance: 0,
        };

        const fireAnalysisResult =
          healthcareFIREIntegration.calculateHealthcareFIREAnalysis(
            userProfile,
            fireGoals,
            currentCosts
          );
        setFireAnalysis(fireAnalysisResult);
      }

      // Calculate total potential savings
      const currentAnnualCost =
        healthProfile.currentPremium * 12 +
        healthProfile.expectedMedicalExpenses;
      const optimizedCost =
        insuranceRecs[0]?.costs.totalAnnualCost || currentAnnualCost;
      const savings = Math.max(0, currentAnnualCost - optimizedCost);
      setTotalSavings(savings);

      if (onSavingsCalculated) {
        onSavingsCalculated(savings);
      }
    } catch (error) {
      console.error("Error calculating healthcare optimizations:", error);
    } finally {
      setIsCalculating(false);
    }
  };

  const handleProfileChange = (
    field: keyof HealthProfileFormData,
    value: any
  ) => {
    setHealthProfile((prev) => ({ ...prev, [field]: value }));
  };

  const renderProfileForm = () => (
    <div className={`space-y-6 ${darkMode ? "text-white" : "text-gray-900"}`}>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Basic Information</h3>

          <div>
            <label className="block text-sm font-medium mb-2">Age</label>
            <input
              type="number"
              value={healthProfile.age}
              onChange={(e) =>
                handleProfileChange("age", parseInt(e.target.value))
              }
              className={`w-full px-3 py-2 border rounded-lg ${
                darkMode
                  ? "bg-gray-700 border-gray-600 text-white"
                  : "bg-white border-gray-300 text-gray-900"
              }`}
              min="18"
              max="100"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Canton</label>
            <select
              value={healthProfile.canton}
              onChange={(e) =>
                handleProfileChange("canton", e.target.value as CantonCode)
              }
              className={`w-full px-3 py-2 border rounded-lg ${
                darkMode
                  ? "bg-gray-700 border-gray-600 text-white"
                  : "bg-white border-gray-300 text-gray-900"
              }`}
            >
              <option value="ZH">Zurich</option>
              <option value="GE">Geneva</option>
              <option value="BS">Basel-Stadt</option>
              <option value="VD">Vaud</option>
              <option value="BE">Bern</option>
              <option value="AG">Aargau</option>
              <option value="LU">Lucerne</option>
              <option value="SG">St. Gallen</option>
              <option value="TI">Ticino</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Annual Income (CHF)
            </label>
            <input
              type="number"
              value={healthProfile.income}
              onChange={(e) =>
                handleProfileChange("income", parseInt(e.target.value))
              }
              className={`w-full px-3 py-2 border rounded-lg ${
                darkMode
                  ? "bg-gray-700 border-gray-600 text-white"
                  : "bg-white border-gray-300 text-gray-900"
              }`}
              min="0"
              step="1000"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Health Status
            </label>
            <select
              value={healthProfile.healthStatus}
              onChange={(e) =>
                handleProfileChange("healthStatus", e.target.value)
              }
              className={`w-full px-3 py-2 border rounded-lg ${
                darkMode
                  ? "bg-gray-700 border-gray-600 text-white"
                  : "bg-white border-gray-300 text-gray-900"
              }`}
            >
              <option value="excellent">Excellent</option>
              <option value="good">Good</option>
              <option value="fair">Fair</option>
              <option value="poor">Poor</option>
            </select>
          </div>
        </div>

        {/* Current Insurance & Family */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Current Insurance & Family</h3>

          <div>
            <label className="block text-sm font-medium mb-2">
              Family Size
            </label>
            <input
              type="number"
              value={healthProfile.familySize}
              onChange={(e) =>
                handleProfileChange("familySize", parseInt(e.target.value))
              }
              className={`w-full px-3 py-2 border rounded-lg ${
                darkMode
                  ? "bg-gray-700 border-gray-600 text-white"
                  : "bg-white border-gray-300 text-gray-900"
              }`}
              min="1"
              max="10"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              checked={healthProfile.hasChildren}
              onChange={(e) =>
                handleProfileChange("hasChildren", e.target.checked)
              }
              className="mr-2"
            />
            <label className="text-sm font-medium">Has Children</label>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Current Monthly Premium (CHF)
            </label>
            <input
              type="number"
              value={healthProfile.currentPremium}
              onChange={(e) =>
                handleProfileChange("currentPremium", parseInt(e.target.value))
              }
              className={`w-full px-3 py-2 border rounded-lg ${
                darkMode
                  ? "bg-gray-700 border-gray-600 text-white"
                  : "bg-white border-gray-300 text-gray-900"
              }`}
              min="0"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Current Deductible (CHF)
            </label>
            <select
              value={healthProfile.currentDeductible}
              onChange={(e) =>
                handleProfileChange(
                  "currentDeductible",
                  parseInt(e.target.value)
                )
              }
              className={`w-full px-3 py-2 border rounded-lg ${
                darkMode
                  ? "bg-gray-700 border-gray-600 text-white"
                  : "bg-white border-gray-300 text-gray-900"
              }`}
            >
              {AVAILABLE_DEDUCTIBLES.map((deductible) => (
                <option key={deductible} value={deductible}>
                  CHF {deductible}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Expected Annual Medical Expenses (CHF)
            </label>
            <input
              type="number"
              value={healthProfile.expectedMedicalExpenses}
              onChange={(e) =>
                handleProfileChange(
                  "expectedMedicalExpenses",
                  parseInt(e.target.value)
                )
              }
              className={`w-full px-3 py-2 border rounded-lg ${
                darkMode
                  ? "bg-gray-700 border-gray-600 text-white"
                  : "bg-white border-gray-300 text-gray-900"
              }`}
              min="0"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Risk Tolerance
            </label>
            <select
              value={healthProfile.riskTolerance}
              onChange={(e) =>
                handleProfileChange("riskTolerance", e.target.value)
              }
              className={`w-full px-3 py-2 border rounded-lg ${
                darkMode
                  ? "bg-gray-700 border-gray-600 text-white"
                  : "bg-white border-gray-300 text-gray-900"
              }`}
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );

  const renderOptimizationResults = () => (
    <div
      className={`space-y-6 ${darkMode ? "text-white" : "text-gray-900"}`}
      data-testid="optimization-results"
    >
      {/* Savings Summary */}
      <div
        className={`p-6 rounded-lg ${darkMode ? "bg-gray-800" : "bg-blue-50"}`}
      >
        <h3 className="text-xl font-bold mb-4">💰 Potential Annual Savings</h3>
        <div
          className="text-3xl font-bold text-blue-600 mb-2"
          data-testid="total-savings"
        >
          CHF {totalSavings.toLocaleString()}
        </div>
        <p className="text-sm opacity-75">
          Based on optimal insurance selection and deductible strategy
        </p>
      </div>

      {/* Deductible Optimization */}
      {deductibleOptimization && (
        <div
          className={`p-6 rounded-lg border ${
            darkMode
              ? "bg-gray-800 border-gray-700"
              : "bg-white border-gray-200"
          }`}
        >
          <h3 className="text-lg font-semibold mb-4">
            🎯 Optimal Deductible Strategy
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div
                className="text-2xl font-bold text-green-600"
                data-testid="recommended-deductible"
              >
                CHF{" "}
                {deductibleOptimization.optimalStrategy.recommendedDeductible}
              </div>
              <div className="text-sm opacity-75">Recommended Deductible</div>
            </div>
            <div className="text-center">
              <div
                className="text-2xl font-bold text-blue-600"
                data-testid="annual-savings"
              >
                CHF{" "}
                {Math.round(
                  deductibleOptimization.optimalStrategy.expectedAnnualSavings
                )}
              </div>
              <div className="text-sm opacity-75">Annual Savings</div>
            </div>
            <div className="text-center">
              <div
                className="text-2xl font-bold text-purple-600"
                data-testid="confidence-level"
              >
                {Math.round(
                  deductibleOptimization.optimalStrategy.confidenceLevel * 100
                )}
                %
              </div>
              <div className="text-sm opacity-75">Confidence Level</div>
            </div>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium">Reasoning:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm opacity-75">
              {deductibleOptimization.optimalStrategy.reasoning.map(
                (reason, index) => (
                  <li key={index}>{reason}</li>
                )
              )}
            </ul>
          </div>
        </div>
      )}

      {/* Top Insurance Recommendations */}
      <div
        className={`p-6 rounded-lg border ${
          darkMode ? "bg-gray-800 border-gray-700" : "bg-white border-gray-200"
        }`}
      >
        <h3 className="text-lg font-semibold mb-4">
          🏥 Top Insurance Recommendations
        </h3>
        <div className="space-y-4">
          {recommendations.slice(0, 3).map((rec, index) => (
            <div
              key={index}
              className={`p-4 rounded-lg border ${
                darkMode ? "border-gray-600" : "border-gray-200"
              }`}
            >
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-medium">{rec.insurerName}</h4>
                  <p className="text-sm opacity-75">
                    {rec.planConfiguration.model} • CHF{" "}
                    {rec.planConfiguration.deductible} deductible
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold">
                    CHF {rec.costs.monthlyPremium}/month
                  </div>
                  <div className="text-sm text-green-600">
                    Save CHF {Math.round(rec.costs.potentialSavings)}/year
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>Pros:</strong>
                  <ul className="list-disc list-inside mt-1 opacity-75">
                    {rec.pros.slice(0, 2).map((pro, i) => (
                      <li key={i}>{pro}</li>
                    ))}
                  </ul>
                </div>
                <div>
                  <strong>Quality:</strong>
                  <div className="mt-1 opacity-75">
                    Rating: {rec.qualityMetrics.customerSatisfaction}/5 ⭐<br />
                    Claims: {rec.qualityMetrics.claimProcessingSpeed} days
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderFIREIntegration = () => (
    <div className={`space-y-6 ${darkMode ? "text-white" : "text-gray-900"}`}>
      {fireAnalysis ? (
        <>
          {/* FIRE Impact Summary */}
          <div
            className={`p-6 rounded-lg ${
              darkMode ? "bg-gray-800" : "bg-orange-50"
            }`}
          >
            <h3 className="text-xl font-bold mb-4">
              🔥 Healthcare Impact on FIRE
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  CHF{" "}
                  {Math.round(
                    fireAnalysis.fireImpact.additionalFIRENumber
                  ).toLocaleString()}
                </div>
                <div className="text-sm opacity-75">Additional FIRE Number</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {fireAnalysis.fireImpact.delayInMonths} months
                </div>
                <div className="text-sm opacity-75">Potential FIRE Delay</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {Math.round(
                    fireAnalysis.fireImpact.healthcareAsPercentageOfFIRE
                  )}
                  %
                </div>
                <div className="text-sm opacity-75">Of FIRE Budget</div>
              </div>
            </div>
          </div>

          {/* Healthcare Projections */}
          <div
            className={`p-6 rounded-lg border ${
              darkMode
                ? "bg-gray-800 border-gray-700"
                : "bg-white border-gray-200"
            }`}
          >
            <h3 className="text-lg font-semibold mb-4">
              📊 Healthcare Cost Projections
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-2">
                  Average Annual Costs in FIRE
                </h4>
                <div className="text-2xl font-bold text-blue-600 mb-2">
                  CHF{" "}
                  {Math.round(
                    fireAnalysis.fireHealthcareProjections.averageAnnualCost
                  ).toLocaleString()}
                </div>
                <p className="text-sm opacity-75">
                  Inflation-adjusted costs until AHV eligibility
                </p>
              </div>
              <div>
                <h4 className="font-medium mb-2">
                  Subsidy Optimization Potential
                </h4>
                <div className="text-2xl font-bold text-green-600 mb-2">
                  CHF{" "}
                  {Math.round(
                    fireAnalysis.fireHealthcareProjections
                      .subsidyOptimizationPotential
                  ).toLocaleString()}
                </div>
                <p className="text-sm opacity-75">
                  Potential savings through subsidy optimization
                </p>
              </div>
            </div>
          </div>

          {/* Optimization Strategies */}
          <div
            className={`p-6 rounded-lg border ${
              darkMode
                ? "bg-gray-800 border-gray-700"
                : "bg-white border-gray-200"
            }`}
          >
            <h3 className="text-lg font-semibold mb-4">
              🎯 FIRE Optimization Strategies
            </h3>
            <div className="space-y-4">
              {fireAnalysis.fireImpact.recommendations.map((rec, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg ${
                    darkMode ? "bg-gray-700" : "bg-gray-50"
                  }`}
                >
                  <p className="text-sm">{rec}</p>
                </div>
              ))}
            </div>
          </div>
        </>
      ) : (
        <div
          className={`p-6 rounded-lg border ${
            darkMode
              ? "bg-gray-800 border-gray-700"
              : "bg-white border-gray-200"
          }`}
        >
          <p className="text-center opacity-75">
            FIRE goals not provided. Connect with FIRE planning to see
            healthcare impact analysis.
          </p>
        </div>
      )}
    </div>
  );

  return (
    <div
      className={`max-w-6xl mx-auto p-6 ${
        darkMode ? "bg-gray-900" : "bg-gray-50"
      } min-h-screen`}
    >
      {/* Header */}
      <div className="mb-8">
        <h1
          className={`text-3xl font-bold mb-2 ${
            darkMode ? "text-white" : "text-gray-900"
          }`}
        >
          🏥 Swiss Healthcare Cost Optimizer
        </h1>
        <p
          className={`text-lg ${darkMode ? "text-gray-300" : "text-gray-600"}`}
        >
          Optimize your Swiss health insurance and integrate with FIRE planning
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="mb-6">
        <nav className="flex space-x-1">
          {[
            { id: "profile", label: "Health Profile", icon: "👤" },
            { id: "optimization", label: "Optimization", icon: "⚡" },
            { id: "fire", label: "FIRE Integration", icon: "🔥" },
            { id: "recommendations", label: "Action Plan", icon: "📋" },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeTab === tab.id
                  ? darkMode
                    ? "bg-blue-600 text-white"
                    : "bg-blue-500 text-white"
                  : darkMode
                  ? "bg-gray-700 text-gray-300 hover:bg-gray-600"
                  : "bg-gray-200 text-gray-700 hover:bg-gray-300"
              }`}
            >
              {tab.icon} {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Loading State */}
      {isCalculating && (
        <div
          className={`p-6 rounded-lg border ${
            darkMode
              ? "bg-gray-800 border-gray-700"
              : "bg-white border-gray-200"
          } mb-6`}
        >
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mr-3"></div>
            <span>Calculating optimal healthcare strategies...</span>
          </div>
        </div>
      )}

      {/* Tab Content */}
      <div className="mb-8">
        {activeTab === "profile" && renderProfileForm()}
        {activeTab === "optimization" && renderOptimizationResults()}
        {activeTab === "fire" && renderFIREIntegration()}
        {activeTab === "recommendations" && (
          <div
            className={`p-6 rounded-lg border ${
              darkMode
                ? "bg-gray-800 border-gray-700"
                : "bg-white border-gray-200"
            }`}
          >
            <h3 className="text-lg font-semibold mb-4">📋 Action Plan</h3>
            <p className="opacity-75">
              Comprehensive action plan coming soon...
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default HealthcareCostOptimizer;
