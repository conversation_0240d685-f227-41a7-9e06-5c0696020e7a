import React, { useState } from 'react';

interface ErrorRecoveryProps {
  error?: Error;
  errorInfo?: React.ErrorInfo;
  onRecover?: () => void;
}

const ErrorRecovery: React.FC<ErrorRecoveryProps> = ({ error, errorInfo, onRecover }) => {
  const [showDetails, setShowDetails] = useState(false);
  const [isRecovering, setIsRecovering] = useState(false);

  const handleRefresh = () => {
    window.location.reload();
  };

  const handleResetData = () => {
    setIsRecovering(true);
    try {
      // Clear all Swiss Budget Pro data
      const keys = Object.keys(localStorage).filter(key => key.startsWith('swissBudgetPro_'));
      keys.forEach(key => localStorage.removeItem(key));
      
      // Set minimal default data
      const defaultData = {
        monthlyIncome: '5000',
        currentAge: '30',
        retirementAge: '65',
        selectedCanton: 'ZH',
        civilStatus: 'single',
        currentSavings: '0',
        expectedReturn: '7',
        inflationRate: '2',
        timestamp: new Date().toISOString(),
        version: '1.0'
      };
      
      localStorage.setItem('swissBudgetPro_default', JSON.stringify(defaultData));
      
      setTimeout(() => {
        if (onRecover) {
          onRecover();
        } else {
          window.location.reload();
        }
      }, 1000);
    } catch (resetError) {
      console.error('Error during reset:', resetError);
      window.location.reload();
    }
  };

  const handleExportErrorLog = () => {
    const errorLog = {
      timestamp: new Date().toISOString(),
      error: {
        message: error?.message,
        stack: error?.stack,
        name: error?.name
      },
      errorInfo: {
        componentStack: errorInfo?.componentStack
      },
      userAgent: navigator.userAgent,
      url: window.location.href,
      localStorage: Object.keys(localStorage).filter(key => key.startsWith('swissBudgetPro_'))
    };

    const blob = new Blob([JSON.stringify(errorLog, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `swiss-budget-pro-error-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 shadow-2xl p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="text-6xl mb-4">⚠️</div>
          <h1 className="text-3xl font-bold text-white mb-2">Application Error</h1>
          <p className="text-blue-200">
            Swiss Budget Pro encountered an unexpected error. Don't worry - your data might still be recoverable.
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6">
            <h3 className="text-red-200 font-semibold mb-2">Error Details:</h3>
            <p className="text-red-100 text-sm font-mono">{error.message}</p>
          </div>
        )}

        {/* Recovery Options */}
        <div className="space-y-4 mb-6">
          <h3 className="text-xl font-semibold text-white mb-4">Recovery Options:</h3>
          
          <button
            onClick={handleRefresh}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
          >
            🔄 Refresh Page
            <span className="text-sm opacity-75">(Try this first)</span>
          </button>

          <button
            onClick={handleResetData}
            disabled={isRecovering}
            className="w-full bg-orange-600 hover:bg-orange-700 disabled:bg-orange-400 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
          >
            {isRecovering ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                Resetting...
              </>
            ) : (
              <>
                🔧 Reset & Refresh
                <span className="text-sm opacity-75">(Clears data, sets defaults)</span>
              </>
            )}
          </button>

          <button
            onClick={handleExportErrorLog}
            className="w-full bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
          >
            📋 Export Error Log
            <span className="text-sm opacity-75">(For bug reporting)</span>
          </button>
        </div>

        {/* Debug Information */}
        <div className="border-t border-white/20 pt-6">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="text-blue-300 hover:text-blue-200 text-sm flex items-center gap-2 mb-4"
          >
            🔍 {showDetails ? 'Hide' : 'Show'} Technical Details
            <span className="text-xs opacity-75">(for debugging)</span>
          </button>

          {showDetails && (
            <div className="bg-black/30 rounded-lg p-4 text-xs font-mono text-gray-300 max-h-64 overflow-y-auto">
              <div className="mb-4">
                <strong className="text-white">Error:</strong>
                <pre className="mt-1 whitespace-pre-wrap">{error?.stack || 'No error stack available'}</pre>
              </div>
              
              {errorInfo?.componentStack && (
                <div className="mb-4">
                  <strong className="text-white">Component Stack:</strong>
                  <pre className="mt-1 whitespace-pre-wrap">{errorInfo.componentStack}</pre>
                </div>
              )}
              
              <div className="mb-4">
                <strong className="text-white">Browser:</strong>
                <div className="mt-1">{navigator.userAgent}</div>
              </div>
              
              <div>
                <strong className="text-white">Local Storage Keys:</strong>
                <div className="mt-1">
                  {Object.keys(localStorage)
                    .filter(key => key.startsWith('swissBudgetPro_'))
                    .map(key => (
                      <div key={key} className="text-yellow-300">{key}</div>
                    ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Help Text */}
        <div className="mt-6 text-center text-sm text-blue-200">
          <p>
            If the problem persists, please{' '}
            <a 
              href="https://github.com/swiss-budget-pro/swiss-budget-pro/issues" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-300 hover:text-blue-200 underline"
            >
              report this issue on GitHub
            </a>
            {' '}with the exported error log.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ErrorRecovery;
