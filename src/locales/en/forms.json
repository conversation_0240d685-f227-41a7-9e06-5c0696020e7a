{"labels": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "address": "Address", "city": "City", "postalCode": "Postal Code", "canton": "Canton", "country": "Country", "birthDate": "Date of Birth", "age": "Age", "currentAge": "Current Age", "retirementAge": "Retirement Age"}, "placeholders": {"enterAmount": "Enter amount", "selectOption": "Select an option", "enterText": "Enter text", "selectDate": "Select date", "enterEmail": "Enter email address", "enterPhone": "Enter phone number"}, "income": {"title": "Income Information", "monthlyIncome": "Monthly Income", "workTime": "Work Time: {{percentage}}%", "companyIncome": "Company Income", "startYear": "Start Year", "growthRate": "Annual Growth Rate", "contractIncome": "Contract Income", "investmentIncome": "Investment Income", "otherIncome": "Other Income"}, "expenses": {"title": "Expense Information", "monthlyExpenses": "Monthly Expenses", "housing": "Housing & Utilities", "food": "Food & Groceries", "transportation": "Transportation", "insurance": "Insurance", "healthcare": "Healthcare", "entertainment": "Entertainment", "travel": "Travel", "shopping": "Shopping", "other": "Other Expenses", "addCategory": "Add Category", "categoryName": "Category Name"}, "savings": {"title": "Savings Information", "currentSavings": "Current Total Savings", "portfolioStart": "Portfolio Start", "monthlySavings": "Monthly Savings", "emergencyFund": "Emergency Fund", "investmentGoals": "Investment Goals", "riskTolerance": "Risk Tolerance", "timeHorizon": "Time Horizon"}, "swiss": {"title": "Swiss Configuration", "canton": "Canton of Residence", "civilStatus": "Civil Status", "hasSecondPillar": "Second Pillar (BVG)", "currentPillar3a": "Current Pillar 3a", "maxPillar3a": "Max Pillar 3a Contribution", "healthInsurance": "Health Insurance Premium", "franchise": "Health Insurance Franchise"}, "validation": {"required": "This field is required", "invalidEmail": "Please enter a valid email address", "invalidPhone": "Please enter a valid phone number", "invalidAmount": "Please enter a valid amount", "minAmount": "Amount must be at least {{min}}", "maxAmount": "Amount cannot exceed {{max}}", "invalidPercentage": "Please enter a percentage between 0 and 100", "invalidAge": "Please enter a valid age", "futureDate": "Date cannot be in the future", "pastDate": "Date cannot be in the past"}, "help": {"monthlyIncome": "Enter your gross monthly income from employment", "workTime": "Percentage of full-time work (100% = full-time)", "companyIncome": "Additional income from your own company or business", "pillar3a": "Tax-advantaged retirement savings (max CHF 7,056 in 2024)", "emergencyFund": "Recommended: 3-6 months of expenses", "riskTolerance": "Your comfort level with investment volatility", "canton": "Your canton of residence affects tax calculations"}, "tooltips": {"info": "More information", "help": "Help", "example": "Example", "calculation": "How this is calculated", "recommendation": "Our recommendation"}}