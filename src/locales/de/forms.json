{"labels": {"firstName": "<PERSON><PERSON><PERSON>", "lastName": "Nachname", "email": "E-Mail", "phone": "Telefon", "address": "<PERSON><PERSON><PERSON>", "city": "Stadt", "postalCode": "<PERSON><PERSON><PERSON><PERSON>", "canton": "<PERSON><PERSON>", "country": "Land", "birthDate": "Geburtsdatum", "age": "Alter", "currentAge": "Aktuelles Alter", "retirementAge": "Pensionsalter"}, "placeholders": {"enterAmount": "<PERSON><PERSON> e<PERSON>ben", "selectOption": "Option auswählen", "enterText": "Text eingeben", "selectDate": "Da<PERSON> ausw<PERSON>en", "enterEmail": "E-Mail-Ad<PERSON><PERSON>", "enterPhone": "Telefonnummer eingeben"}, "income": {"title": "Einkommensinformationen", "monthlyIncome": "Mona<PERSON>einkommen", "workTime": "Arbeitszeit: {{percentage}}%", "companyIncome": "Firmeneinkommen", "startYear": "<PERSON><PERSON>r", "growthRate": "Jährliche Wachstumsrate", "contractIncome": "Auftragseinkommen", "investmentIncome": "Kapitalerträge", "otherIncome": "Sonstiges Einkommen"}, "expenses": {"title": "Ausgabeninformationen", "monthlyExpenses": "Monatsausgaben", "housing": "Wohnen & Nebenkosten", "food": "Lebensmittel & Einkäufe", "transportation": "Transport", "insurance": "Versicherungen", "healthcare": "Gesundheit", "entertainment": "Unterhaltung", "travel": "<PERSON><PERSON><PERSON>", "shopping": "Einkäufe", "other": "Sonstige Ausgaben", "addCategory": "<PERSON><PERSON><PERSON>", "categoryName": "<PERSON><PERSON><PERSON><PERSON>"}, "savings": {"title": "Sparinformationen", "currentSavings": "Aktuelle Gesamtersparnisse", "portfolioStart": "Portfolio-Start", "monthlySavings": "Monatliche Ersparnisse", "emergencyFund": "Notfallfonds", "investmentGoals": "<PERSON><PERSON><PERSON><PERSON>", "riskTolerance": "Risikotoleranz", "timeHorizon": "Zeithorizont"}, "swiss": {"title": "Schweizer Konfiguration", "canton": "<PERSON><PERSON><PERSON><PERSON>", "civilStatus": "Zivilstand", "hasSecondPillar": "Zweite Säule (BVG)", "currentPillar3a": "Aktuelle Säule 3a", "maxPillar3a": "Max. Säule 3a Beitrag", "healthInsurance": "Krankenversicherungsprämie", "franchise": "Krankenversicherungs-Franchise"}, "validation": {"required": "<PERSON><PERSON> ist erford<PERSON>lich", "invalidEmail": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein", "invalidPhone": "Bitte geben Si<PERSON> eine gültige Telefonnummer ein", "invalidAmount": "Bitte geben Si<PERSON> einen gültigen Betrag ein", "minAmount": "Betrag muss mindestens {{min}} sein", "maxAmount": "Betrag darf {{max}} nicht überschreiten", "invalidPercentage": "Bitte geben Sie einen Prozentsatz zwischen 0 und 100 ein", "invalidAge": "Bitte geben Si<PERSON> ein gültiges Alter ein", "futureDate": "Datum darf nicht in der Zukunft liegen", "pastDate": "Datum darf nicht in der Vergangenheit liegen"}, "help": {"monthlyIncome": "Geben Sie Ihr monatliches Bruttoeinkommen aus der Anstellung ein (vor Abzug von Steuern und Sozialversicherungen)", "workTime": "Arbeitspensum in Prozent der Vollzeitarbeit (100% = Vollzeit, 80% = 4 Tage pro Woche)", "companyIncome": "Zusätzliches Einkommen aus selbständiger Tätigkeit oder eigenem Unternehmen", "pillar3a": "Gebundene Selbstvorsorge mit Steuervorteilen (max. CHF 7'056 für Angestellte, CHF 35'280 für Selbständige in 2024)", "emergencyFund": "Notfallfonds: Empfohlen sind 3-6 Monate Ihrer fixen Ausgaben als Liquiditätsreserve", "riskTolerance": "Ihre Bereitschaft, Schwankungen bei Ihren Anlagen zu akzeptieren (höheres Risiko = höhere erwartete Rendite)", "canton": "<PERSON><PERSON> Wohnkanton bestimmt die Höhe der Kantons- und Gemeindesteuern", "civilStatus": "Ihr Zivilstand beeinflusst die Steuerberechnung (Verheiratete werden oft gemeinsam veranlagt)", "franchise": "Selbstbehalt bei der Krankenversicherung (höhere Franchise = tiefere Prämie)"}, "tooltips": {"info": "Weitere Informationen", "help": "<PERSON><PERSON><PERSON>", "example": "Beispiel", "calculation": "Wie dies berechnet wird", "recommendation": "Unsere Empfehlung"}}