import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";

// Translation resources - using dynamic loading instead of static imports
const resources = {
  en: {
    common: {
      appName: "Swiss Budget Pro",
      tagline: "Your Advanced Financial Command Center",
      navigation: {
        income: "Income",
        expenses: "Expenses",
        savings: "Savings",
        analysis: "Analysis",
        taxOptimization: "Tax Optimization",
        reports: "Reports",
        overview: "Overview",
        budgetPlan: "Budget Plan",
        targetGoal: "Target Goal",
        companyGrowth: "Company Growth",
        swissTaxOptimizer: "Swiss Tax Optimizer",
        economicData: "Economic Data",
        advancedAnalytics: "Advanced Analytics",
        swissRelocation: "Swiss Relocation",
        dataHistory: "Data & History",
        textProjections: "Text Projections",
        budgetViz: "Budget Viz",
        projectionChart: "Projection Chart",
        pdfReport: "PDF Report",
      },
      actions: {
        save: "Save",
        export: "Export",
        calculate: "Calculate",
        reset: "Reset",
        print: "Print/Save as PDF",
        close: "Close",
        edit: "Edit",
        delete: "Delete",
        add: "Add",
        update: "Update",
        cancel: "Cancel",
        confirm: "Confirm",
        back: "Back",
        next: "Next",
        finish: "Finish",
      },
      status: {
        loading: "Loading...",
        saving: "Saving...",
        saved: "Saved",
        error: "Error",
        success: "Success",
        warning: "Warning",
        info: "Information",
      },
      time: {
        daily: "Daily",
        weekly: "Weekly",
        monthly: "Monthly",
        quarterly: "Quarterly",
        yearly: "Yearly",
        annual: "Annual",
      },
      currency: {
        chf: "CHF",
        symbol: "CHF",
        format: "CHF {{amount}}",
      },
      percentage: {
        symbol: "%",
        format: "{{value}}%",
      },
      theme: {
        light: "Light mode",
        dark: "Dark mode",
        toggle: "Toggle theme",
      },
      language: {
        english: "English",
        german: "Deutsch",
        french: "Français",
        italian: "Italiano",
        romansh: "Rumantsch",
        switch: "Switch language",
        current: "Current language",
      },
      metrics: {
        savingsRate: "savings",
        fireProgress: "to FIRE",
        monthsToFire: "months to FIRE",
        yearsToRetire: "yrs to retire",
        netWorth: "net worth",
      },
      general: {
        yes: "Yes",
        no: "No",
        optional: "Optional",
        required: "Required",
        recommended: "Recommended",
        advanced: "Advanced",
        basic: "Basic",
        total: "Total",
        subtotal: "Subtotal",
        average: "Average",
        minimum: "Minimum",
        maximum: "Maximum",
        current: "Current",
        projected: "Projected",
        actual: "Actual",
        estimated: "Estimated",
      },
    },
    financial: {},
    swiss: {},
    forms: {},
    reports: {},
    errors: {},
  },
  de: {
    common: {
      appName: "Swiss Budget Pro",
      tagline: "Ihr fortschrittliches Finanz-Kommandozentrum",
    },
    financial: {},
    swiss: {},
    forms: {},
    reports: {},
    errors: {},
  },
};

// Language detection configuration
const detectionOptions = {
  // Order of language detection methods
  order: ["localStorage", "navigator", "htmlTag", "path", "subdomain"],

  // Cache user language preference
  caches: ["localStorage"],

  // localStorage key for language preference
  lookupLocalStorage: "swissBudgetPro_language",

  // Don't detect from query string or cookie for security
  excludeCacheFor: ["cimode"],

  // Check all available languages
  checkWhitelist: true,
};

// Initialize i18next
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    // Available languages
    supportedLngs: ["en", "de"],

    // Fallback language
    fallbackLng: "en",

    // Default namespace
    defaultNS: "common",

    // Available namespaces
    ns: ["common", "financial", "swiss", "forms", "reports", "errors"],

    // Translation resources
    resources,

    // Language detection
    detection: detectionOptions,

    // Interpolation options
    interpolation: {
      escapeValue: false, // React already escapes values
      formatSeparator: ",",
    },

    // React options
    react: {
      useSuspense: false, // Disable suspense for now
      bindI18n: "languageChanged",
      bindI18nStore: "",
      transEmptyNodeValue: "",
      transSupportBasicHtmlNodes: true,
      transKeepBasicHtmlNodesFor: ["br", "strong", "i", "em"],
    },

    // Debug mode (disable in production)
    debug: process.env.NODE_ENV === "development",

    // Key separator
    keySeparator: ".",

    // Namespace separator
    nsSeparator: ":",

    // Return objects for missing keys
    returnObjects: false,

    // Return empty string for missing keys
    returnEmptyString: false,

    // Return null for missing keys
    returnNull: false,

    // Postprocess missing keys
    saveMissing: process.env.NODE_ENV === "development",

    // Missing key handler for development
    missingKeyHandler: (lng, ns, key, fallbackValue) => {
      if (process.env.NODE_ENV === "development") {
        console.warn(
          `Missing translation key: ${ns}:${key} for language: ${lng}`
        );
      }
    },
  });

export default i18n;
