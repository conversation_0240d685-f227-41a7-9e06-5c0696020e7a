/**
 * Swiss Budget Pro - Privacy Controls & Data Management
 * 
 * Provides granular privacy controls, data transparency,
 * and Swiss/GDPR compliance features.
 */

// Data categories for granular privacy control
export enum DataCategory {
  FINANCIAL_DATA = 'financial_data',
  PERSONAL_INFO = 'personal_info',
  USAGE_ANALYTICS = 'usage_analytics',
  PREFERENCES = 'preferences',
  CACHE_DATA = 'cache_data',
  TEMPORARY_DATA = 'temporary_data',
}

// Privacy settings for each data category
export interface PrivacySettings {
  [DataCategory.FINANCIAL_DATA]: {
    enabled: boolean;
    retentionDays: number;
    encryptionRequired: boolean;
    exportAllowed: boolean;
  };
  [DataCategory.PERSONAL_INFO]: {
    enabled: boolean;
    retentionDays: number;
    encryptionRequired: boolean;
    exportAllowed: boolean;
  };
  [DataCategory.USAGE_ANALYTICS]: {
    enabled: boolean;
    retentionDays: number;
    anonymized: boolean;
    exportAllowed: boolean;
  };
  [DataCategory.PREFERENCES]: {
    enabled: boolean;
    retentionDays: number;
    encryptionRequired: boolean;
    exportAllowed: boolean;
  };
  [DataCategory.CACHE_DATA]: {
    enabled: boolean;
    retentionDays: number;
    autoCleanup: boolean;
    exportAllowed: boolean;
  };
  [DataCategory.TEMPORARY_DATA]: {
    enabled: boolean;
    retentionDays: number;
    autoCleanup: boolean;
    exportAllowed: boolean;
  };
}

// Data inventory item
export interface DataInventoryItem {
  id: string;
  category: DataCategory;
  description: string;
  dataType: string;
  createdAt: Date;
  lastModified: Date;
  size: number; // in bytes
  encrypted: boolean;
  retentionDate?: Date;
}

// Privacy consent record
export interface ConsentRecord {
  id: string;
  purpose: string;
  dataCategories: DataCategory[];
  granted: boolean;
  timestamp: Date;
  version: string;
  ipAddress?: string; // For audit purposes
}

// User rights request
export interface UserRightsRequest {
  id: string;
  type: 'access' | 'rectification' | 'erasure' | 'portability';
  dataCategories: DataCategory[];
  status: 'pending' | 'processing' | 'completed' | 'rejected';
  requestedAt: Date;
  completedAt?: Date;
  reason?: string;
}

/**
 * Privacy Controls Manager
 * 
 * Manages user privacy settings, data inventory, consent,
 * and user rights in compliance with Swiss and GDPR requirements.
 */
export class PrivacyControlsManager {
  private static instance: PrivacyControlsManager;
  private settings: PrivacySettings;
  private dataInventory: Map<string, DataInventoryItem> = new Map();
  private consentRecords: ConsentRecord[] = [];
  private userRightsRequests: UserRightsRequest[] = [];

  private constructor() {
    this.settings = this.getDefaultPrivacySettings();
    this.loadPrivacySettings();
  }

  public static getInstance(): PrivacyControlsManager {
    if (!PrivacyControlsManager.instance) {
      PrivacyControlsManager.instance = new PrivacyControlsManager();
    }
    return PrivacyControlsManager.instance;
  }

  /**
   * Get default privacy settings (privacy-first approach)
   */
  private getDefaultPrivacySettings(): PrivacySettings {
    return {
      [DataCategory.FINANCIAL_DATA]: {
        enabled: true,
        retentionDays: 2555, // 7 years (Swiss financial record keeping)
        encryptionRequired: true,
        exportAllowed: true,
      },
      [DataCategory.PERSONAL_INFO]: {
        enabled: true,
        retentionDays: 365, // 1 year default
        encryptionRequired: true,
        exportAllowed: true,
      },
      [DataCategory.USAGE_ANALYTICS]: {
        enabled: false, // Opt-in by default
        retentionDays: 90,
        anonymized: true,
        exportAllowed: true,
      },
      [DataCategory.PREFERENCES]: {
        enabled: true,
        retentionDays: 730, // 2 years
        encryptionRequired: false,
        exportAllowed: true,
      },
      [DataCategory.CACHE_DATA]: {
        enabled: true,
        retentionDays: 30,
        autoCleanup: true,
        exportAllowed: false,
      },
      [DataCategory.TEMPORARY_DATA]: {
        enabled: true,
        retentionDays: 1,
        autoCleanup: true,
        exportAllowed: false,
      },
    };
  }

  /**
   * Update privacy settings for a specific data category
   */
  public updatePrivacySettings(
    category: DataCategory,
    settings: Partial<PrivacySettings[DataCategory]>
  ): void {
    this.settings[category] = {
      ...this.settings[category],
      ...settings,
    };

    this.savePrivacySettings();
    this.auditPrivacyChange(category, settings);
  }

  /**
   * Get current privacy settings
   */
  public getPrivacySettings(): PrivacySettings {
    return { ...this.settings };
  }

  /**
   * Check if data collection is allowed for a category
   */
  public isDataCollectionAllowed(category: DataCategory): boolean {
    return this.settings[category].enabled;
  }

  /**
   * Register data in the inventory
   */
  public registerData(
    id: string,
    category: DataCategory,
    description: string,
    dataType: string,
    size: number,
    encrypted: boolean = false
  ): void {
    if (!this.isDataCollectionAllowed(category)) {
      throw new Error(`Data collection not allowed for category: ${category}`);
    }

    const now = new Date();
    const retentionDays = this.settings[category].retentionDays;
    const retentionDate = new Date(now.getTime() + retentionDays * 24 * 60 * 60 * 1000);

    const item: DataInventoryItem = {
      id,
      category,
      description,
      dataType,
      createdAt: now,
      lastModified: now,
      size,
      encrypted,
      retentionDate,
    };

    this.dataInventory.set(id, item);
    this.saveDataInventory();
  }

  /**
   * Update existing data in inventory
   */
  public updateDataInventory(id: string, updates: Partial<DataInventoryItem>): void {
    const item = this.dataInventory.get(id);
    if (!item) {
      throw new Error(`Data item not found: ${id}`);
    }

    const updatedItem = {
      ...item,
      ...updates,
      lastModified: new Date(),
    };

    this.dataInventory.set(id, updatedItem);
    this.saveDataInventory();
  }

  /**
   * Remove data from inventory
   */
  public removeDataFromInventory(id: string): void {
    this.dataInventory.delete(id);
    this.saveDataInventory();
  }

  /**
   * Get complete data inventory
   */
  public getDataInventory(): DataInventoryItem[] {
    return Array.from(this.dataInventory.values());
  }

  /**
   * Get data inventory by category
   */
  public getDataByCategory(category: DataCategory): DataInventoryItem[] {
    return this.getDataInventory().filter(item => item.category === category);
  }

  /**
   * Record user consent
   */
  public recordConsent(
    purpose: string,
    dataCategories: DataCategory[],
    granted: boolean,
    version: string = '1.0'
  ): string {
    const consent: ConsentRecord = {
      id: this.generateId(),
      purpose,
      dataCategories,
      granted,
      timestamp: new Date(),
      version,
    };

    this.consentRecords.push(consent);
    this.saveConsentRecords();

    return consent.id;
  }

  /**
   * Get consent history
   */
  public getConsentHistory(): ConsentRecord[] {
    return [...this.consentRecords];
  }

  /**
   * Check if consent is granted for specific purpose and data categories
   */
  public hasConsent(purpose: string, dataCategories: DataCategory[]): boolean {
    const relevantConsents = this.consentRecords.filter(
      consent => consent.purpose === purpose && consent.granted
    );

    if (relevantConsents.length === 0) {
      return false;
    }

    // Check if all required data categories have consent
    const latestConsent = relevantConsents[relevantConsents.length - 1];
    return dataCategories.every(category =>
      latestConsent.dataCategories.includes(category)
    );
  }

  /**
   * Submit user rights request
   */
  public submitUserRightsRequest(
    type: UserRightsRequest['type'],
    dataCategories: DataCategory[],
    reason?: string
  ): string {
    const request: UserRightsRequest = {
      id: this.generateId(),
      type,
      dataCategories,
      status: 'pending',
      requestedAt: new Date(),
      reason,
    };

    this.userRightsRequests.push(request);
    this.saveUserRightsRequests();

    // Auto-process certain requests
    this.processUserRightsRequest(request.id);

    return request.id;
  }

  /**
   * Process user rights request
   */
  private async processUserRightsRequest(requestId: string): Promise<void> {
    const request = this.userRightsRequests.find(r => r.id === requestId);
    if (!request) {
      return;
    }

    request.status = 'processing';

    try {
      switch (request.type) {
        case 'access':
          await this.processAccessRequest(request);
          break;
        case 'rectification':
          await this.processRectificationRequest(request);
          break;
        case 'erasure':
          await this.processErasureRequest(request);
          break;
        case 'portability':
          await this.processPortabilityRequest(request);
          break;
      }

      request.status = 'completed';
      request.completedAt = new Date();
    } catch (error) {
      request.status = 'rejected';
      request.reason = error instanceof Error ? error.message : 'Unknown error';
    }

    this.saveUserRightsRequests();
  }

  /**
   * Process data access request (Right to Access)
   */
  private async processAccessRequest(request: UserRightsRequest): Promise<void> {
    const relevantData = this.getDataInventory().filter(item =>
      request.dataCategories.includes(item.category)
    );

    // Generate access report
    const report = {
      requestId: request.id,
      requestedAt: request.requestedAt,
      dataCategories: request.dataCategories,
      dataItems: relevantData,
      privacySettings: this.getPrivacySettings(),
      consentHistory: this.getConsentHistory(),
    };

    // In a real implementation, this would be provided to the user
    console.log('Data Access Report:', report);
  }

  /**
   * Process data rectification request (Right to Rectification)
   */
  private async processRectificationRequest(request: UserRightsRequest): Promise<void> {
    // This would typically involve providing a form for users to correct their data
    // For now, we just mark the request as requiring manual intervention
    throw new Error('Rectification requests require manual processing');
  }

  /**
   * Process data erasure request (Right to Erasure / Right to be Forgotten)
   */
  private async processErasureRequest(request: UserRightsRequest): Promise<void> {
    const itemsToDelete = this.getDataInventory().filter(item =>
      request.dataCategories.includes(item.category)
    );

    for (const item of itemsToDelete) {
      // Check if erasure is legally required or allowed
      if (this.canEraseData(item)) {
        this.removeDataFromInventory(item.id);
        // In a real implementation, also delete the actual data
      }
    }
  }

  /**
   * Process data portability request (Right to Data Portability)
   */
  private async processPortabilityRequest(request: UserRightsRequest): Promise<void> {
    const relevantData = this.getDataInventory().filter(item =>
      request.dataCategories.includes(item.category) &&
      this.settings[item.category].exportAllowed
    );

    // Generate portable data export
    const exportData = {
      exportedAt: new Date(),
      dataCategories: request.dataCategories,
      data: relevantData,
      format: 'JSON',
      version: '1.0',
    };

    // In a real implementation, this would be provided as a download
    console.log('Data Export:', exportData);
  }

  /**
   * Check if data can be erased (considering legal retention requirements)
   */
  private canEraseData(item: DataInventoryItem): boolean {
    // Swiss financial records must be kept for 7 years
    if (item.category === DataCategory.FINANCIAL_DATA) {
      const sevenYearsAgo = new Date();
      sevenYearsAgo.setFullYear(sevenYearsAgo.getFullYear() - 7);
      return item.createdAt < sevenYearsAgo;
    }

    // Other data can be erased based on retention settings
    return item.retentionDate ? new Date() > item.retentionDate : true;
  }

  /**
   * Perform automatic data cleanup based on retention policies
   */
  public performDataCleanup(): void {
    const now = new Date();
    const itemsToCleanup: string[] = [];

    for (const [id, item] of this.dataInventory) {
      const categorySettings = this.settings[item.category];
      
      if (categorySettings.autoCleanup && item.retentionDate && now > item.retentionDate) {
        if (this.canEraseData(item)) {
          itemsToCleanup.push(id);
        }
      }
    }

    // Remove expired items
    for (const id of itemsToCleanup) {
      this.removeDataFromInventory(id);
    }

    console.log(`Data cleanup completed. Removed ${itemsToCleanup.length} expired items.`);
  }

  /**
   * Get privacy compliance status
   */
  public getComplianceStatus(): {
    gdprCompliant: boolean;
    swissCompliant: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    // Check GDPR compliance
    const hasValidConsent = this.consentRecords.some(c => c.granted);
    if (!hasValidConsent) {
      issues.push('No valid consent records found');
    }

    // Check Swiss compliance
    const financialDataSettings = this.settings[DataCategory.FINANCIAL_DATA];
    if (financialDataSettings.retentionDays < 2555) { // 7 years
      issues.push('Financial data retention period below Swiss legal requirement');
    }

    return {
      gdprCompliant: issues.length === 0,
      swissCompliant: issues.length === 0,
      issues,
    };
  }

  // Utility methods
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private auditPrivacyChange(category: DataCategory, changes: any): void {
    console.log(`Privacy settings updated for ${category}:`, changes);
    // In a real implementation, this would be logged to an audit trail
  }

  // Persistence methods (using localStorage for now)
  private savePrivacySettings(): void {
    localStorage.setItem('swiss-budget-privacy-settings', JSON.stringify(this.settings));
  }

  private loadPrivacySettings(): void {
    const saved = localStorage.getItem('swiss-budget-privacy-settings');
    if (saved) {
      try {
        this.settings = { ...this.getDefaultPrivacySettings(), ...JSON.parse(saved) };
      } catch (error) {
        console.error('Failed to load privacy settings:', error);
      }
    }
  }

  private saveDataInventory(): void {
    const inventoryArray = Array.from(this.dataInventory.entries());
    localStorage.setItem('swiss-budget-data-inventory', JSON.stringify(inventoryArray));
  }

  private saveConsentRecords(): void {
    localStorage.setItem('swiss-budget-consent-records', JSON.stringify(this.consentRecords));
  }

  private saveUserRightsRequests(): void {
    localStorage.setItem('swiss-budget-user-rights', JSON.stringify(this.userRightsRequests));
  }
}

// Export singleton instance
export const privacyControls = PrivacyControlsManager.getInstance();
