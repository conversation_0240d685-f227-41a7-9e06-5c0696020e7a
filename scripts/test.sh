#!/usr/bin/env bash

# Swiss Budget Pro Test Runner
# Comprehensive testing script with different modes
# NOTE: This script should be run within the Nix shell environment

set -e

echo "🧪 Swiss Budget Pro Test Suite (Nix Environment)"
echo "================================================="

# Check if we're in a Nix shell
if [ -z "$IN_NIX_SHELL" ]; then
    echo "⚠️  Warning: Not running in Nix shell!"
    echo "   Run 'nix-shell' first for consistent environment"
    echo "   Or use 'direnv allow' for automatic loading"
    echo ""
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if dependencies are installed
check_dependencies() {
    print_status "Checking dependencies..."

    # Verify we have Node.js available
    if ! command -v node &> /dev/null; then
        print_error "Node.js not found! Make sure you're in the Nix shell."
        exit 1
    fi

    # Verify we have npm available
    if ! command -v npm &> /dev/null; then
        print_error "npm not found! Make sure you're in the Nix shell."
        exit 1
    fi

    print_status "Node.js version: $(node --version)"
    print_status "npm version: $(npm --version)"

    if [ ! -d "node_modules" ]; then
        print_warning "Dependencies not found. Installing..."
        npm install
    fi

    print_success "Dependencies ready"
}

# Run different test modes
run_unit_tests() {
    print_status "Running unit tests..."
    npm run test:run -- test/calculations/ test/hooks/
}

run_component_tests() {
    print_status "Running component tests..."
    npm run test:run -- test/components/
}

run_integration_tests() {
    print_status "Running integration tests..."
    npm run test:run -- test/integration/
}

run_all_tests() {
    print_status "Running all tests..."
    npm run test:run
}

run_coverage() {
    print_status "Running tests with coverage..."
    npm run test:coverage
}

run_watch_mode() {
    print_status "Starting watch mode..."
    npm run test:watch
}

run_ui_mode() {
    print_status "Starting UI mode..."
    npm run test:ui
}

# Main execution
case "${1:-all}" in
    "unit")
        check_dependencies
        run_unit_tests
        ;;
    "component")
        check_dependencies
        run_component_tests
        ;;
    "integration")
        check_dependencies
        run_integration_tests
        ;;
    "coverage")
        check_dependencies
        run_coverage
        ;;
    "watch")
        check_dependencies
        run_watch_mode
        ;;
    "ui")
        check_dependencies
        run_ui_mode
        ;;
    "all"|"")
        check_dependencies
        run_all_tests
        ;;
    "help"|"-h"|"--help")
        echo "Usage: $0 [mode]"
        echo ""
        echo "Modes:"
        echo "  unit        Run unit tests only"
        echo "  component   Run component tests only"
        echo "  integration Run integration tests only"
        echo "  coverage    Run all tests with coverage report"
        echo "  watch       Run tests in watch mode"
        echo "  ui          Run tests with UI interface"
        echo "  all         Run all tests (default)"
        echo "  help        Show this help message"
        exit 0
        ;;
    *)
        print_error "Unknown mode: $1"
        print_status "Use '$0 help' for available options"
        exit 1
        ;;
esac

print_success "Test execution completed!"
