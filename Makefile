# Swiss Budget Pro - Makefile
# All commands assume execution within Nix shell environment

.PHONY: help install dev build test test-watch test-ui test-coverage clean shell setup

# Default target
help: ## Show this help message
	@echo "🚀 Swiss Budget Pro - Development Commands"
	@echo "=========================================="
	@echo ""
	@echo "📋 Available targets:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "⚠️  Note: All commands should be run within the Nix shell environment"
	@echo "   Run 'make shell' or 'nix-shell' first"

shell: ## Enter the Nix development shell
	@echo "🐚 Entering Nix shell..."
	nix-shell

setup: ## Initial project setup (run once)
	@echo "🔧 Setting up Swiss Budget Pro development environment..."
	@if [ ! -d ".git" ]; then \
		echo "📦 Initializing git repository..."; \
		git init; \
	fi
	@if command -v direnv >/dev/null 2>&1; then \
		echo "🔄 Allowing direnv..."; \
		direnv allow; \
	else \
		echo "💡 Consider installing direnv for automatic environment loading"; \
		echo "   nix-env -iA nixpkgs.direnv"; \
	fi
	@echo "✅ Setup complete! Run 'make shell' or 'nix-shell' to start developing"

install: ## Install npm dependencies
	@echo "📦 Installing dependencies..."
	npm install
	@echo "✅ Dependencies installed"

dev: ## Start development server
	@echo "🚀 Starting development server..."
	npm run dev

build: ## Build for production
	@echo "🏗️  Building for production..."
	npm run build

preview: ## Preview production build
	@echo "👀 Previewing production build..."
	npm run preview

# Testing targets
test: ## Run all tests
	@echo "🧪 Running all tests..."
	npm test

test-watch: ## Run tests in watch mode
	@echo "👀 Running tests in watch mode..."
	npm run test:watch

test-ui: ## Run tests with UI interface
	@echo "🎨 Starting test UI..."
	npm run test:ui

test-coverage: ## Run tests with coverage report
	@echo "📊 Running tests with coverage..."
	npm run test:coverage

test-unit: ## Run unit tests only
	@echo "🔬 Running unit tests..."
	./scripts/test.sh unit

test-component: ## Run component tests only
	@echo "🧩 Running component tests..."
	./scripts/test.sh component

test-integration: ## Run integration tests only
	@echo "🔗 Running integration tests..."
	./scripts/test.sh integration

# Utility targets
clean: ## Clean build artifacts and dependencies
	@echo "🧹 Cleaning build artifacts..."
	rm -rf node_modules
	rm -rf dist
	rm -rf coverage
	rm -rf .npm-cache
	rm -rf .npm-global
	@echo "✅ Clean complete"

lint: ## Run linting (if configured)
	@echo "🔍 Running linter..."
	@if [ -f "package.json" ] && npm list eslint >/dev/null 2>&1; then \
		npm run lint; \
	else \
		echo "⚠️  No linter configured"; \
	fi

format: ## Format code (if configured)
	@echo "💅 Formatting code..."
	@if [ -f "package.json" ] && npm list prettier >/dev/null 2>&1; then \
		npm run format; \
	else \
		echo "⚠️  No formatter configured"; \
	fi

check-env: ## Check if running in Nix environment
	@echo "🔍 Checking environment..."
	@if [ -n "$$IN_NIX_SHELL" ]; then \
		echo "✅ Running in Nix shell"; \
		echo "📦 Node.js: $$(node --version)"; \
		echo "📦 npm: $$(npm --version)"; \
	else \
		echo "⚠️  Not running in Nix shell!"; \
		echo "   Run 'make shell' or 'nix-shell' first"; \
		exit 1; \
	fi

status: ## Show project status
	@echo "📊 Swiss Budget Pro - Project Status"
	@echo "===================================="
	@echo ""
	@make check-env
	@echo ""
	@if [ -d "node_modules" ]; then \
		echo "✅ Dependencies installed"; \
	else \
		echo "❌ Dependencies not installed (run 'make install')"; \
	fi
	@echo ""
	@if [ -d ".git" ]; then \
		echo "🌿 Git branch: $$(git branch --show-current 2>/dev/null || echo 'unknown')"; \
		echo "📊 Git status:"; \
		git status --porcelain | head -3; \
	fi

# Development workflow targets
start: install dev ## Install dependencies and start development server

test-all: test-unit test-component test-integration ## Run all test categories

ci: install test-coverage build ## Run CI pipeline (install, test, build)

# Quick development commands
quick-test: ## Quick test run (no coverage)
	npm run test:run

quick-build: ## Quick build without cleaning
	npm run build

# Help for common workflows
workflows: ## Show common development workflows
	@echo "🔄 Common Development Workflows"
	@echo "==============================="
	@echo ""
	@echo "🚀 First time setup:"
	@echo "   make setup"
	@echo "   make shell"
	@echo "   make start"
	@echo ""
	@echo "🧪 Testing workflow:"
	@echo "   make test-watch    # During development"
	@echo "   make test-coverage # Before commit"
	@echo "   make test-ui       # Visual debugging"
	@echo ""
	@echo "🏗️  Build workflow:"
	@echo "   make build         # Production build"
	@echo "   make preview       # Test production build"
	@echo ""
	@echo "🔧 Maintenance:"
	@echo "   make clean         # Clean everything"
	@echo "   make install       # Reinstall dependencies"
