/**
 * Comprehensive UI Tests for SmartDashboard Component
 *
 * This test suite covers:
 * - Financial metric display and formatting
 * - FIRE progress visualization
 * - User interaction handling
 * - Dark/light mode rendering
 * - Real-time calculation updates
 * - Error state handling
 */

import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import SmartDashboard from "../../../src/components/improved/SmartDashboard";

// Mock react-i18next
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Test data fixtures
const mockUserProgress = {
  hasBasicInfo: true,
  hasIncomeData: true,
  hasExpenseData: true,
  hasSavingsGoals: true,
  hasSwissConfig: true,
  completionPercentage: 85,
};

const mockFinancialData = {
  totalMonthlyIncome: 8000,
  totalExpenses: 5000,
  savingsRate: 37.5,
  fireProgress: 25,
  monthsToFire: 180,
  currentBalance: 150000,
  currentAge: 32,
  retirementAge: 55,
  finalProjection: {
    fireAge: 52,
    fireProgress: 25,
    monthsToFire: 180,
  },
};

const mockIncompleteUserProgress = {
  hasBasicInfo: false,
  hasIncomeData: false,
  hasExpenseData: false,
  hasSavingsGoals: false,
  hasSwissConfig: false,
  completionPercentage: 15,
};

const mockZeroFinancialData = {
  totalMonthlyIncome: 0,
  totalExpenses: 0,
  savingsRate: 0,
  fireProgress: 0,
  monthsToFire: 0,
  currentBalance: 0,
  currentAge: 25,
  retirementAge: 65,
};

describe("SmartDashboard Component", () => {
  const mockOnNavigate = vi.fn();
  const mockOnShowOnboarding = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("Basic Rendering", () => {
    it("renders without crashing", () => {
      render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={mockFinancialData}
          darkMode={false}
        />
      );

      expect(screen.getByText("🚀 Your FIRE Journey")).toBeInTheDocument();
    });

    it("displays completion percentage correctly", () => {
      render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={mockFinancialData}
          darkMode={false}
        />
      );

      expect(screen.getByText("85% Complete")).toBeInTheDocument();
    });

    it("renders all metric cards", () => {
      render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={mockFinancialData}
          darkMode={false}
        />
      );

      // Check for metric labels
      expect(screen.getByText("Monthly Income")).toBeInTheDocument();
      expect(screen.getByText("Savings Rate")).toBeInTheDocument();
      expect(screen.getByText("FIRE Progress")).toBeInTheDocument();
    });
  });

  describe("Financial Metrics Display", () => {
    it("displays monthly income with correct formatting", () => {
      render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={mockFinancialData}
          darkMode={false}
        />
      );

      expect(screen.getByText("CHF 8,000")).toBeInTheDocument();
    });

    it("displays savings rate with correct precision", () => {
      render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={mockFinancialData}
          darkMode={false}
        />
      );

      expect(screen.getByText("37.5%")).toBeInTheDocument();
    });

    it("displays FIRE progress as percentage", () => {
      render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={mockFinancialData}
          darkMode={false}
        />
      );

      expect(screen.getByText("25%")).toBeInTheDocument();
    });

    it("handles zero values gracefully", () => {
      render(
        <SmartDashboard
          userProgress={mockIncompleteUserProgress}
          financialData={mockZeroFinancialData}
          darkMode={false}
        />
      );

      expect(screen.getByText("CHF 0")).toBeInTheDocument();
      expect(screen.getByText("0.0%")).toBeInTheDocument();
    });

    it("handles large numbers with proper formatting", () => {
      const largeFinancialData = {
        ...mockFinancialData,
        totalMonthlyIncome: 25000,
        currentBalance: 1500000,
      };

      render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={largeFinancialData}
          darkMode={false}
        />
      );

      expect(screen.getByText("CHF 25,000")).toBeInTheDocument();
    });
  });

  describe("FIRE Progress Visualization", () => {
    it("displays FIRE age when available", () => {
      render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={mockFinancialData}
          darkMode={false}
        />
      );

      // Should display the projected FIRE age
      const fireProjection = screen.getByTestId("fire-projection");
      expect(fireProjection).toBeInTheDocument();
    });

    it("shows appropriate message for early FIRE achievers", () => {
      const earlyFireData = {
        ...mockFinancialData,
        fireProgress: 95,
        monthsToFire: 12,
        finalProjection: {
          fireAge: 35,
          fireProgress: 95,
          monthsToFire: 12,
        },
      };

      render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={earlyFireData}
          darkMode={false}
        />
      );

      // Should indicate close to FIRE
      expect(screen.getByText("95%")).toBeInTheDocument();
    });

    it("handles missing FIRE projection data", () => {
      const noProjectionData = {
        ...mockFinancialData,
        finalProjection: undefined,
      };

      render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={noProjectionData}
          darkMode={false}
        />
      );

      // Should still render without crashing
      expect(screen.getByText("🚀 Your FIRE Journey")).toBeInTheDocument();
    });
  });

  describe("Dark Mode Support", () => {
    it("applies dark mode classes correctly", () => {
      const { container } = render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={mockFinancialData}
          darkMode={true}
        />
      );

      const dashboard = container.querySelector(".smart-dashboard");
      expect(dashboard).toBeInTheDocument();

      // Check for dark mode gradient classes
      const progressSection = container.querySelector(
        ".bg-gradient-to-r.from-blue-900\\/50"
      );
      expect(progressSection).toBeInTheDocument();
    });

    it("applies light mode classes correctly", () => {
      const { container } = render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={mockFinancialData}
          darkMode={false}
        />
      );

      // Check for light mode gradient classes
      const progressSection = container.querySelector(
        ".bg-gradient-to-r.from-blue-50"
      );
      expect(progressSection).toBeInTheDocument();
    });

    it("switches between themes without losing data", () => {
      const { rerender } = render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={mockFinancialData}
          darkMode={false}
        />
      );

      expect(screen.getByText("CHF 8,000")).toBeInTheDocument();

      rerender(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={mockFinancialData}
          darkMode={true}
        />
      );

      // Data should still be displayed
      expect(screen.getByText("CHF 8,000")).toBeInTheDocument();
    });
  });

  describe("User Interaction", () => {
    it("calls onNavigate when action buttons are clicked", async () => {
      render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={mockFinancialData}
          darkMode={false}
          onNavigate={mockOnNavigate}
        />
      );

      // Look for action buttons (these might be in recommendations)
      const actionButtons = screen.queryAllByRole("button");

      if (actionButtons.length > 0) {
        fireEvent.click(actionButtons[0]);
        await waitFor(() => {
          expect(mockOnNavigate).toHaveBeenCalled();
        });
      }
    });

    it("calls onShowOnboarding when setup is incomplete", async () => {
      render(
        <SmartDashboard
          userProgress={mockIncompleteUserProgress}
          financialData={mockZeroFinancialData}
          darkMode={false}
          onShowOnboarding={mockOnShowOnboarding}
        />
      );

      // Look for onboarding trigger
      const onboardingButton =
        screen.queryByText(/complete setup/i) ||
        screen.queryByText(/get started/i);

      if (onboardingButton) {
        fireEvent.click(onboardingButton);
        await waitFor(() => {
          expect(mockOnShowOnboarding).toHaveBeenCalled();
        });
      }
    });
  });

  describe("Progress States", () => {
    it("shows appropriate recommendations for low savings rate", () => {
      const lowSavingsData = {
        ...mockFinancialData,
        savingsRate: 5,
      };

      render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={lowSavingsData}
          darkMode={false}
        />
      );

      // Should show optimization recommendations
      expect(screen.getByText("5.0%")).toBeInTheDocument();
    });

    it("shows different recommendations for high savings rate", () => {
      const highSavingsData = {
        ...mockFinancialData,
        savingsRate: 60,
      };

      render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={highSavingsData}
          darkMode={false}
        />
      );

      expect(screen.getByText("60.0%")).toBeInTheDocument();
    });

    it("handles incomplete user progress appropriately", () => {
      render(
        <SmartDashboard
          userProgress={mockIncompleteUserProgress}
          financialData={mockZeroFinancialData}
          darkMode={false}
        />
      );

      expect(screen.getByText("15% Complete")).toBeInTheDocument();
    });
  });

  describe("Error Handling", () => {
    it("handles undefined financial data gracefully", () => {
      const undefinedData = {
        totalMonthlyIncome: undefined,
        totalExpenses: undefined,
        savingsRate: undefined,
        fireProgress: undefined,
        monthsToFire: undefined,
        currentBalance: undefined,
        currentAge: 30,
        retirementAge: 65,
      } as any;

      render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={undefinedData}
          darkMode={false}
        />
      );

      // Should render with fallback values
      expect(screen.getByText("CHF 0")).toBeInTheDocument();
      expect(screen.getByText("0.0%")).toBeInTheDocument();
    });

    it("handles negative values appropriately", () => {
      const negativeData = {
        ...mockFinancialData,
        savingsRate: -10,
        fireProgress: -5,
      };

      render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={negativeData}
          darkMode={false}
        />
      );

      // Should display negative values or handle them appropriately
      expect(screen.getByText("-10.0%")).toBeInTheDocument();
    });

    it("handles very large numbers without breaking layout", () => {
      const extremeData = {
        ...mockFinancialData,
        totalMonthlyIncome: 999999999,
        currentBalance: 999999999999,
      };

      render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={extremeData}
          darkMode={false}
        />
      );

      // Should format large numbers appropriately
      const incomeText = screen.getByText(/CHF 999,999,999/);
      expect(incomeText).toBeInTheDocument();
    });
  });

  describe("Accessibility", () => {
    it("has proper ARIA labels for metrics", () => {
      render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={mockFinancialData}
          darkMode={false}
        />
      );

      // Check for accessible elements by finding the dashboard container
      const dashboard = screen.getByText("🚀 Your FIRE Journey").closest("div");
      expect(dashboard).toBeInTheDocument();
    });

    it("supports keyboard navigation", () => {
      render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={mockFinancialData}
          darkMode={false}
          onNavigate={mockOnNavigate}
        />
      );

      // Check for focusable elements
      const buttons = screen.getAllByRole("button");
      buttons.forEach((button) => {
        expect(button).toBeVisible();
      });
    });

    it("provides meaningful text for screen readers", () => {
      render(
        <SmartDashboard
          userProgress={mockUserProgress}
          financialData={mockFinancialData}
          darkMode={false}
        />
      );

      // Check for descriptive text
      expect(screen.getByText("Monthly Income")).toBeInTheDocument();
      expect(screen.getByText("Savings Rate")).toBeInTheDocument();
      expect(screen.getByText("FIRE Progress")).toBeInTheDocument();
    });
  });
});
