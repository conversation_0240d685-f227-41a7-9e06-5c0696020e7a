/**
 * Comprehensive UI Tests for OnboardingWizard Component
 * 
 * This test suite covers:
 * - Multi-step wizard navigation
 * - Form validation and input handling
 * - Swiss-specific input validation (cantons, income)
 * - Progress tracking and completion
 * - Error state handling
 * - Dark/light mode rendering
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import OnboardingWizard from '../../../src/components/improved/OnboardingWizard';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

describe('OnboardingWizard Component', () => {
  const mockOnComplete = vi.fn();
  const mockOnClose = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering and Navigation', () => {
    it('renders welcome step initially', () => {
      render(
        <OnboardingWizard
          isOpen={true}
          onComplete={mockOnComplete}
          onClose={mockOnClose}
          darkMode={false}
        />
      );
      
      expect(screen.getByText('🚀')).toBeInTheDocument();
      expect(screen.getByText(/Swiss Budget Pro helps you plan/)).toBeInTheDocument();
    });

    it('shows progress indicator', () => {
      render(
        <OnboardingWizard
          isOpen={true}
          onComplete={mockOnComplete}
          onClose={mockOnClose}
          darkMode={false}
        />
      );
      
      // Should show step progress
      const progressElements = screen.getAllByText(/Step \d+ of \d+/);
      expect(progressElements.length).toBeGreaterThan(0);
    });

    it('navigates to next step when next button is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <OnboardingWizard
          isOpen={true}
          onComplete={mockOnComplete}
          onClose={mockOnClose}
          darkMode={false}
        />
      );
      
      const nextButton = screen.getByText(/Get Started|Next/i);
      await user.click(nextButton);
      
      // Should move to personal info step
      await waitFor(() => {
        expect(screen.getByText(/Personal Information|Age/i)).toBeInTheDocument();
      });
    });

    it('allows navigation back to previous step', async () => {
      const user = userEvent.setup();
      
      render(
        <OnboardingWizard
          isOpen={true}
          onComplete={mockOnComplete}
          onClose={mockOnClose}
          darkMode={false}
        />
      );
      
      // Navigate forward first
      const nextButton = screen.getByText(/Get Started|Next/i);
      await user.click(nextButton);
      
      // Then navigate back
      await waitFor(async () => {
        const backButton = screen.queryByText(/Back/i);
        if (backButton) {
          await user.click(backButton);
          expect(screen.getByText('🚀')).toBeInTheDocument();
        }
      });
    });

    it('closes when close button is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <OnboardingWizard
          isOpen={true}
          onComplete={mockOnComplete}
          onClose={mockOnClose}
          darkMode={false}
        />
      );
      
      const closeButton = screen.getByLabelText(/close/i) || screen.getByText('×');
      await user.click(closeButton);
      
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  describe('Personal Information Step', () => {
    beforeEach(async () => {
      const user = userEvent.setup();
      
      render(
        <OnboardingWizard
          isOpen={true}
          onComplete={mockOnComplete}
          onClose={mockOnClose}
          darkMode={false}
        />
      );
      
      // Navigate to personal info step
      const nextButton = screen.getByText(/Get Started|Next/i);
      await user.click(nextButton);
    });

    it('renders age input field', async () => {
      await waitFor(() => {
        const ageInput = screen.getByLabelText(/age/i) || screen.getByPlaceholderText(/age/i);
        expect(ageInput).toBeInTheDocument();
      });
    });

    it('renders canton selection dropdown', async () => {
      await waitFor(() => {
        const cantonSelect = screen.getByLabelText(/canton/i) || screen.getByDisplayValue(/Select.*canton/i);
        expect(cantonSelect).toBeInTheDocument();
      });
    });

    it('validates age input correctly', async () => {
      const user = userEvent.setup();
      
      await waitFor(async () => {
        const ageInput = screen.getByLabelText(/age/i) || screen.getByPlaceholderText(/age/i);
        
        // Test invalid age
        await user.clear(ageInput);
        await user.type(ageInput, '15');
        
        const nextButton = screen.getByText(/Next/i);
        await user.click(nextButton);
        
        // Should show validation error or prevent progression
        expect(screen.getByDisplayValue('15')).toBeInTheDocument();
      });
    });

    it('accepts valid Swiss canton codes', async () => {
      const user = userEvent.setup();
      
      await waitFor(async () => {
        const cantonSelect = screen.getByLabelText(/canton/i) || screen.getByDisplayValue(/Select.*canton/i);
        
        // Select a valid canton
        await user.selectOptions(cantonSelect, 'ZH');
        expect(screen.getByDisplayValue('ZH')).toBeInTheDocument();
      });
    });

    it('prevents progression with incomplete data', async () => {
      const user = userEvent.setup();
      
      await waitFor(async () => {
        const nextButton = screen.getByText(/Next/i);
        
        // Try to proceed without filling required fields
        await user.click(nextButton);
        
        // Should remain on the same step
        expect(screen.getByLabelText(/age/i) || screen.getByPlaceholderText(/age/i)).toBeInTheDocument();
      });
    });
  });

  describe('Income Step', () => {
    beforeEach(async () => {
      const user = userEvent.setup();
      
      render(
        <OnboardingWizard
          isOpen={true}
          onComplete={mockOnComplete}
          onClose={mockOnClose}
          darkMode={false}
        />
      );
      
      // Navigate to income step
      const nextButton = screen.getByText(/Get Started|Next/i);
      await user.click(nextButton);
      
      // Fill personal info and proceed
      await waitFor(async () => {
        const ageInput = screen.getByLabelText(/age/i) || screen.getByPlaceholderText(/age/i);
        await user.type(ageInput, '30');
        
        const cantonSelect = screen.getByLabelText(/canton/i) || screen.getByDisplayValue(/Select.*canton/i);
        await user.selectOptions(cantonSelect, 'ZH');
        
        const nextBtn = screen.getByText(/Next/i);
        await user.click(nextBtn);
      });
    });

    it('renders primary income input field', async () => {
      await waitFor(() => {
        const incomeInput = screen.getByLabelText(/primary.*income/i) || 
                           screen.getByPlaceholderText(/8000|income/i);
        expect(incomeInput).toBeInTheDocument();
      });
    });

    it('validates income input format', async () => {
      const user = userEvent.setup();
      
      await waitFor(async () => {
        const incomeInput = screen.getByLabelText(/primary.*income/i) || 
                           screen.getByPlaceholderText(/8000|income/i);
        
        // Test valid income
        await user.type(incomeInput, '8500');
        expect(screen.getByDisplayValue('8500')).toBeInTheDocument();
        
        // Test invalid income (negative)
        await user.clear(incomeInput);
        await user.type(incomeInput, '-1000');
        
        // Should handle negative values appropriately
        expect(incomeInput).toBeInTheDocument();
      });
    });

    it('handles additional income toggle', async () => {
      const user = userEvent.setup();
      
      await waitFor(async () => {
        const additionalIncomeToggle = screen.queryByLabelText(/additional.*income/i) ||
                                      screen.queryByText(/additional.*income/i);
        
        if (additionalIncomeToggle) {
          await user.click(additionalIncomeToggle);
          
          // Should show additional income input
          const additionalInput = screen.getByPlaceholderText(/1000|additional/i);
          expect(additionalInput).toBeInTheDocument();
        }
      });
    });

    it('formats Swiss currency correctly', async () => {
      const user = userEvent.setup();
      
      await waitFor(async () => {
        const incomeInput = screen.getByLabelText(/primary.*income/i) || 
                           screen.getByPlaceholderText(/8000|income/i);
        
        await user.type(incomeInput, '12500');
        
        // Should accept Swiss currency format
        expect(screen.getByDisplayValue('12500')).toBeInTheDocument();
      });
    });
  });

  describe('Dark Mode Support', () => {
    it('applies dark mode styles correctly', () => {
      const { container } = render(
        <OnboardingWizard
          isOpen={true}
          onComplete={mockOnComplete}
          onClose={mockOnClose}
          darkMode={true}
        />
      );
      
      // Check for dark mode classes
      const modal = container.querySelector('.bg-gray-800') || 
                   container.querySelector('.bg-gray-900');
      expect(modal).toBeInTheDocument();
    });

    it('applies light mode styles correctly', () => {
      const { container } = render(
        <OnboardingWizard
          isOpen={true}
          onComplete={mockOnComplete}
          onClose={mockOnClose}
          darkMode={false}
        />
      );
      
      // Check for light mode classes
      const modal = container.querySelector('.bg-white') || 
                   container.querySelector('.bg-gray-50');
      expect(modal).toBeInTheDocument();
    });

    it('switches themes without losing form data', async () => {
      const user = userEvent.setup();
      
      const { rerender } = render(
        <OnboardingWizard
          isOpen={true}
          onComplete={mockOnComplete}
          onClose={mockOnClose}
          darkMode={false}
        />
      );
      
      // Navigate and fill some data
      const nextButton = screen.getByText(/Get Started|Next/i);
      await user.click(nextButton);
      
      await waitFor(async () => {
        const ageInput = screen.getByLabelText(/age/i) || screen.getByPlaceholderText(/age/i);
        await user.type(ageInput, '35');
      });
      
      // Switch to dark mode
      rerender(
        <OnboardingWizard
          isOpen={true}
          onComplete={mockOnComplete}
          onClose={mockOnClose}
          darkMode={true}
        />
      );
      
      // Data should be preserved
      expect(screen.getByDisplayValue('35')).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('shows validation errors for invalid inputs', async () => {
      const user = userEvent.setup();
      
      render(
        <OnboardingWizard
          isOpen={true}
          onComplete={mockOnComplete}
          onClose={mockOnClose}
          darkMode={false}
        />
      );
      
      // Navigate to personal info
      const nextButton = screen.getByText(/Get Started|Next/i);
      await user.click(nextButton);
      
      await waitFor(async () => {
        const ageInput = screen.getByLabelText(/age/i) || screen.getByPlaceholderText(/age/i);
        
        // Enter invalid age
        await user.type(ageInput, '200');
        
        const nextBtn = screen.getByText(/Next/i);
        await user.click(nextBtn);
        
        // Should show error or prevent progression
        expect(screen.getByDisplayValue('200')).toBeInTheDocument();
      });
    });

    it('clears validation errors when input becomes valid', async () => {
      const user = userEvent.setup();
      
      render(
        <OnboardingWizard
          isOpen={true}
          onComplete={mockOnComplete}
          onClose={mockOnClose}
          darkMode={false}
        />
      );
      
      // Navigate to personal info
      const nextButton = screen.getByText(/Get Started|Next/i);
      await user.click(nextButton);
      
      await waitFor(async () => {
        const ageInput = screen.getByLabelText(/age/i) || screen.getByPlaceholderText(/age/i);
        
        // Enter invalid then valid age
        await user.type(ageInput, '200');
        await user.clear(ageInput);
        await user.type(ageInput, '30');
        
        // Should accept valid input
        expect(screen.getByDisplayValue('30')).toBeInTheDocument();
      });
    });
  });

  describe('Completion Flow', () => {
    it('calls onComplete when wizard is finished', async () => {
      const user = userEvent.setup();
      
      render(
        <OnboardingWizard
          isOpen={true}
          onComplete={mockOnComplete}
          onClose={mockOnClose}
          darkMode={false}
        />
      );
      
      // Complete the wizard flow
      const nextButton = screen.getByText(/Get Started|Next/i);
      await user.click(nextButton);
      
      // Fill personal info
      await waitFor(async () => {
        const ageInput = screen.getByLabelText(/age/i) || screen.getByPlaceholderText(/age/i);
        await user.type(ageInput, '30');
        
        const cantonSelect = screen.getByLabelText(/canton/i) || screen.getByDisplayValue(/Select.*canton/i);
        await user.selectOptions(cantonSelect, 'ZH');
        
        const nextBtn = screen.getByText(/Next/i);
        await user.click(nextBtn);
      });
      
      // Fill income info
      await waitFor(async () => {
        const incomeInput = screen.getByLabelText(/primary.*income/i) || 
                           screen.getByPlaceholderText(/8000|income/i);
        await user.type(incomeInput, '8500');
        
        const completeButton = screen.getByText(/Complete|Finish/i);
        await user.click(completeButton);
      });
      
      // Should call onComplete with user data
      await waitFor(() => {
        expect(mockOnComplete).toHaveBeenCalledWith(
          expect.objectContaining({
            age: 30,
            canton: 'ZH',
            monthlyIncome: 8500,
          })
        );
      });
    });

    it('provides complete user data to onComplete callback', async () => {
      const user = userEvent.setup();
      
      render(
        <OnboardingWizard
          isOpen={true}
          onComplete={mockOnComplete}
          onClose={mockOnClose}
          darkMode={false}
        />
      );
      
      // Complete minimal flow
      const nextButton = screen.getByText(/Get Started|Next/i);
      await user.click(nextButton);
      
      await waitFor(async () => {
        const ageInput = screen.getByLabelText(/age/i) || screen.getByPlaceholderText(/age/i);
        await user.type(ageInput, '25');
        
        const cantonSelect = screen.getByLabelText(/canton/i) || screen.getByDisplayValue(/Select.*canton/i);
        await user.selectOptions(cantonSelect, 'GE');
        
        const nextBtn = screen.getByText(/Next/i);
        await user.click(nextBtn);
      });
      
      await waitFor(async () => {
        const incomeInput = screen.getByLabelText(/primary.*income/i) || 
                           screen.getByPlaceholderText(/8000|income/i);
        await user.type(incomeInput, '7500');
        
        const completeButton = screen.getByText(/Complete|Finish/i);
        await user.click(completeButton);
      });
      
      await waitFor(() => {
        expect(mockOnComplete).toHaveBeenCalledWith(
          expect.objectContaining({
            age: 25,
            canton: 'GE',
            monthlyIncome: 7500,
          })
        );
      });
    });
  });

  describe('Accessibility', () => {
    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      
      render(
        <OnboardingWizard
          isOpen={true}
          onComplete={mockOnComplete}
          onClose={mockOnClose}
          darkMode={false}
        />
      );
      
      // Should be able to navigate with Tab key
      await user.tab();
      const focusedElement = document.activeElement;
      expect(focusedElement).toBeInTheDocument();
    });

    it('has proper ARIA labels', () => {
      render(
        <OnboardingWizard
          isOpen={true}
          onComplete={mockOnComplete}
          onClose={mockOnClose}
          darkMode={false}
        />
      );
      
      // Check for modal ARIA attributes
      const modal = screen.getByRole('dialog') || screen.getByLabelText(/onboarding/i);
      expect(modal).toBeInTheDocument();
    });

    it('manages focus correctly', async () => {
      const user = userEvent.setup();
      
      render(
        <OnboardingWizard
          isOpen={true}
          onComplete={mockOnComplete}
          onClose={mockOnClose}
          darkMode={false}
        />
      );
      
      // Focus should be managed when navigating steps
      const nextButton = screen.getByText(/Get Started|Next/i);
      await user.click(nextButton);
      
      await waitFor(() => {
        const firstInput = screen.getByLabelText(/age/i) || screen.getByPlaceholderText(/age/i);
        expect(firstInput).toBeInTheDocument();
      });
    });
  });
});
