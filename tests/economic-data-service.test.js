/**
 * Swiss Economic Data Integration Tests
 * Comprehensive test suite for real-time economic data integration
 */

// Mock the SwissEconomicDataService for testing
const SwissEconomicDataService = {
  cache: {
    snbData: null,
    marketData: null,
    inflationData: null,
    lastUpdate: null,
    cacheExpiry: 24 * 60 * 60 * 1000
  },

  async fetchSNBData() {
    const mockSNBData = {
      policyRate: 1.75,
      inflationTarget: 2.0,
      currentInflation: 1.4,
      chfStrengthIndex: 0.92,
      economicGrowthForecast: 1.2,
      unemploymentRate: 2.1,
      lastUpdated: new Date().toISOString(),
      confidence: 'high',
      source: 'SNB Official Data'
    };
    this.cache.snbData = mockSNBData;
    this.cache.lastUpdate = Date.now();
    return mockSNBData;
  },

  async fetchMarketData() {
    const mockMarketData = {
      smiIndex: 11847,
      smiChange: 0.8,
      spiIndex: 15234,
      spiYearReturn: 6.2,
      bondYield10Y: 0.68,
      bondYield2Y: 0.45,
      volatilityIndex: 16.2,
      marketSentiment: 'neutral',
      lastUpdated: new Date().toISOString(),
      source: 'SIX Swiss Exchange'
    };
    this.cache.marketData = mockMarketData;
    return mockMarketData;
  },

  async fetchInflationData() {
    const mockInflationData = {
      currentCPI: 1.4,
      coreCPI: 1.2,
      housingCosts: 2.1,
      healthcareCosts: 3.2,
      energyCosts: -2.1,
      foodCosts: 0.8,
      forecast12M: 1.6,
      forecast24M: 1.8,
      lastUpdated: new Date().toISOString(),
      source: 'Federal Statistical Office'
    };
    this.cache.inflationData = mockInflationData;
    return mockInflationData;
  },

  async getAllEconomicData() {
    const now = Date.now();
    const cacheValid = this.cache.lastUpdate && (now - this.cache.lastUpdate) < this.cache.cacheExpiry;

    if (cacheValid && this.cache.snbData && this.cache.marketData && this.cache.inflationData) {
      return {
        snb: this.cache.snbData,
        market: this.cache.marketData,
        inflation: this.cache.inflationData,
        cached: true
      };
    }

    const [snbData, marketData, inflationData] = await Promise.all([
      this.fetchSNBData(),
      this.fetchMarketData(),
      this.fetchInflationData()
    ]);

    return {
      snb: snbData,
      market: marketData,
      inflation: inflationData,
      cached: false
    };
  },

  calculateDynamicReturns(assetAllocation = { stocks: 70, bonds: 25, cash: 5 }) {
    const economicData = this.cache;
    if (!economicData.snbData || !economicData.marketData) {
      return this.getDefaultReturns();
    }

    const snb = economicData.snbData;
    const market = economicData.marketData;

    const stockReturn = this.calculateStockReturn(market, snb);
    const bondReturn = this.calculateBondReturn(market, snb);
    const cashReturn = snb.policyRate * 0.8;

    const portfolioReturn = (
      (assetAllocation.stocks / 100) * stockReturn +
      (assetAllocation.bonds / 100) * bondReturn +
      (assetAllocation.cash / 100) * cashReturn
    );

    return {
      stocks: stockReturn,
      bonds: bondReturn,
      cash: cashReturn,
      portfolio: portfolioReturn,
      inflation: snb.currentInflation,
      realReturn: portfolioReturn - snb.currentInflation,
      confidence: this.calculateConfidence(market, snb),
      lastUpdated: new Date().toISOString()
    };
  },

  calculateStockReturn(market, snb) {
    const baseReturn = 7.0;
    let adjustment = 0;

    if (market.spiYearReturn > 15) adjustment -= 1.0;
    else if (market.spiYearReturn < -10) adjustment += 1.5;

    if (market.volatilityIndex > 25) adjustment -= 0.5;
    else if (market.volatilityIndex < 15) adjustment += 0.3;

    if (snb.economicGrowthForecast > 2.0) adjustment += 0.5;
    else if (snb.economicGrowthForecast < 0.5) adjustment -= 1.0;

    return Math.max(3.0, Math.min(12.0, baseReturn + adjustment));
  },

  calculateBondReturn(market, snb) {
    const baseReturn = market.bondYield10Y + 0.5;
    let adjustment = 0;

    if (snb.policyRate > 2.0) adjustment += 0.3;
    else if (snb.policyRate < 0.5) adjustment -= 0.2;

    return Math.max(0.5, Math.min(6.0, baseReturn + adjustment));
  },

  calculateConfidence(market, snb) {
    let confidence = 100;

    if (market.volatilityIndex > 25) confidence -= 20;
    if (market.volatilityIndex > 35) confidence -= 20;

    if (snb.currentInflation > 4.0 || snb.currentInflation < -1.0) confidence -= 15;
    if (snb.economicGrowthForecast < -1.0) confidence -= 25;

    return Math.max(50, confidence);
  },

  checkAlertThresholds(userProfile) {
    const alerts = [];
    const economicData = this.cache;

    if (!economicData.snbData || !economicData.inflationData) return alerts;

    const snb = economicData.snbData;
    const inflation = economicData.inflationData;

    if (inflation.currentCPI > 3.0) {
      alerts.push({
        type: 'inflation_high',
        severity: 'warning',
        title: 'High Inflation Alert',
        message: `Swiss inflation at ${inflation.currentCPI}% exceeds 3% threshold`,
        impact: 'Consider adjusting your return assumptions and increasing equity allocation',
        action: 'Review your FIRE projections with higher inflation assumptions'
      });
    }

    if (inflation.currentCPI < 0) {
      alerts.push({
        type: 'deflation',
        severity: 'info',
        title: 'Deflation Detected',
        message: `Swiss inflation at ${inflation.currentCPI}% indicates deflationary pressure`,
        impact: 'Your purchasing power may increase, but investment returns may be lower',
        action: 'Consider more conservative return assumptions'
      });
    }

    if (snb.policyRate !== userProfile.lastKnownPolicyRate) {
      const direction = snb.policyRate > (userProfile.lastKnownPolicyRate || 0) ? 'increased' : 'decreased';
      alerts.push({
        type: 'policy_rate_change',
        severity: 'info',
        title: 'SNB Policy Rate Change',
        message: `SNB ${direction} policy rate to ${snb.policyRate}%`,
        impact: direction === 'increased' ? 'Higher bond yields, potential market volatility' : 'Lower bond yields, potential asset price increases',
        action: 'Consider rebalancing your portfolio allocation'
      });
    }

    if (economicData.marketData && economicData.marketData.volatilityIndex > 30) {
      alerts.push({
        type: 'high_volatility',
        severity: 'warning',
        title: 'High Market Volatility',
        message: `Swiss market volatility at ${economicData.marketData.volatilityIndex}% is elevated`,
        impact: 'Increased uncertainty in short-term projections',
        action: 'Consider stress-testing your FIRE plan with volatile market scenarios'
      });
    }

    return alerts;
  },

  getDefaultReturns() {
    return {
      stocks: 7.0,
      bonds: 2.5,
      cash: 1.0,
      portfolio: 5.5,
      inflation: 1.5,
      realReturn: 4.0,
      confidence: 75,
      lastUpdated: new Date().toISOString()
    };
  }
};

// Test Suite
describe('Swiss Economic Data Integration', () => {

  beforeEach(() => {
    // Reset cache before each test
    SwissEconomicDataService.cache = {
      snbData: null,
      marketData: null,
      inflationData: null,
      lastUpdate: null,
      cacheExpiry: 24 * 60 * 60 * 1000
    };
  });

  describe('Data Fetching', () => {
    test('should fetch SNB data successfully', async () => {
      const data = await SwissEconomicDataService.fetchSNBData();

      expect(data).toHaveProperty('policyRate');
      expect(data).toHaveProperty('currentInflation');
      expect(data).toHaveProperty('economicGrowthForecast');
      expect(data).toHaveProperty('unemploymentRate');
      expect(data.source).toBe('SNB Official Data');
      expect(typeof data.policyRate).toBe('number');
      expect(data.policyRate).toBeGreaterThanOrEqual(0);
    });

    test('should fetch market data successfully', async () => {
      const data = await SwissEconomicDataService.fetchMarketData();

      expect(data).toHaveProperty('smiIndex');
      expect(data).toHaveProperty('bondYield10Y');
      expect(data).toHaveProperty('volatilityIndex');
      expect(data.source).toBe('SIX Swiss Exchange');
      expect(typeof data.smiIndex).toBe('number');
      expect(data.smiIndex).toBeGreaterThan(0);
    });

    test('should fetch inflation data successfully', async () => {
      const data = await SwissEconomicDataService.fetchInflationData();

      expect(data).toHaveProperty('currentCPI');
      expect(data).toHaveProperty('coreCPI');
      expect(data).toHaveProperty('housingCosts');
      expect(data).toHaveProperty('forecast12M');
      expect(data.source).toBe('Federal Statistical Office');
      expect(typeof data.currentCPI).toBe('number');
    });

    test('should fetch all economic data', async () => {
      const data = await SwissEconomicDataService.getAllEconomicData();

      expect(data).toHaveProperty('snb');
      expect(data).toHaveProperty('market');
      expect(data).toHaveProperty('inflation');
      expect(data).toHaveProperty('cached');
      expect(data.cached).toBe(false); // First fetch should not be cached
    });

    test('should use cache when available', async () => {
      // First fetch
      await SwissEconomicDataService.getAllEconomicData();

      // Second fetch should use cache
      const data = await SwissEconomicDataService.getAllEconomicData();
      expect(data.cached).toBe(true);
    });
  });

  describe('Dynamic Return Calculations', () => {
    beforeEach(async () => {
      // Populate cache with test data
      await SwissEconomicDataService.getAllEconomicData();
    });

    test('should calculate dynamic returns with default allocation', () => {
      const returns = SwissEconomicDataService.calculateDynamicReturns();

      expect(returns).toHaveProperty('stocks');
      expect(returns).toHaveProperty('bonds');
      expect(returns).toHaveProperty('cash');
      expect(returns).toHaveProperty('portfolio');
      expect(returns).toHaveProperty('inflation');
      expect(returns).toHaveProperty('realReturn');
      expect(returns).toHaveProperty('confidence');

      expect(returns.stocks).toBeGreaterThan(0);
      expect(returns.bonds).toBeGreaterThan(0);
      expect(returns.cash).toBeGreaterThan(0);
      expect(returns.portfolio).toBeGreaterThan(0);
      expect(returns.confidence).toBeGreaterThanOrEqual(50);
      expect(returns.confidence).toBeLessThanOrEqual(100);
    });

    test('should calculate returns with custom allocation', () => {
      const customAllocation = { stocks: 80, bonds: 15, cash: 5 };
      const returns = SwissEconomicDataService.calculateDynamicReturns(customAllocation);

      expect(returns.portfolio).toBeGreaterThan(0);
      // Portfolio return should be weighted average
      const expectedReturn = (80/100) * returns.stocks + (15/100) * returns.bonds + (5/100) * returns.cash;
      expect(Math.abs(returns.portfolio - expectedReturn)).toBeLessThan(0.01);
    });

    test('should adjust stock returns based on market conditions', () => {
      // Test with high volatility
      SwissEconomicDataService.cache.marketData.volatilityIndex = 35;
      const highVolReturns = SwissEconomicDataService.calculateDynamicReturns();

      // Test with low volatility
      SwissEconomicDataService.cache.marketData.volatilityIndex = 10;
      const lowVolReturns = SwissEconomicDataService.calculateDynamicReturns();

      expect(lowVolReturns.stocks).toBeGreaterThan(highVolReturns.stocks);
    });

    test('should adjust bond returns based on interest rates', () => {
      // Test with high policy rate
      SwissEconomicDataService.cache.snbData.policyRate = 3.0;
      const highRateReturns = SwissEconomicDataService.calculateDynamicReturns();

      // Test with low policy rate
      SwissEconomicDataService.cache.snbData.policyRate = 0.25;
      const lowRateReturns = SwissEconomicDataService.calculateDynamicReturns();

      expect(highRateReturns.bonds).toBeGreaterThan(lowRateReturns.bonds);
    });

    test('should calculate confidence scores correctly', () => {
      // Normal conditions
      const normalConfidence = SwissEconomicDataService.calculateConfidence(
        { volatilityIndex: 15 },
        { currentInflation: 2.0, economicGrowthForecast: 1.5 }
      );

      // High volatility conditions
      const highVolConfidence = SwissEconomicDataService.calculateConfidence(
        { volatilityIndex: 40 },
        { currentInflation: 2.0, economicGrowthForecast: 1.5 }
      );

      expect(normalConfidence).toBeGreaterThan(highVolConfidence);
      expect(normalConfidence).toBeLessThanOrEqual(100);
      expect(highVolConfidence).toBeGreaterThanOrEqual(50);
    });

    test('should return default values when cache is empty', () => {
      SwissEconomicDataService.cache.snbData = null;
      SwissEconomicDataService.cache.marketData = null;

      const returns = SwissEconomicDataService.calculateDynamicReturns();
      const defaultReturns = SwissEconomicDataService.getDefaultReturns();

      expect(returns).toEqual(defaultReturns);
    });
  });

  describe('Economic Alert System', () => {
    beforeEach(async () => {
      await SwissEconomicDataService.getAllEconomicData();
    });

    test('should generate high inflation alert', () => {
      SwissEconomicDataService.cache.inflationData.currentCPI = 3.5;

      const alerts = SwissEconomicDataService.checkAlertThresholds({});
      const inflationAlert = alerts.find(a => a.type === 'inflation_high');

      expect(inflationAlert).toBeDefined();
      expect(inflationAlert.severity).toBe('warning');
      expect(inflationAlert.title).toBe('High Inflation Alert');
    });

    test('should generate deflation alert', () => {
      SwissEconomicDataService.cache.inflationData.currentCPI = -0.5;

      const alerts = SwissEconomicDataService.checkAlertThresholds({});
      const deflationAlert = alerts.find(a => a.type === 'deflation');

      expect(deflationAlert).toBeDefined();
      expect(deflationAlert.severity).toBe('info');
      expect(deflationAlert.title).toBe('Deflation Detected');
    });

    test('should generate policy rate change alert', () => {
      const userProfile = { lastKnownPolicyRate: 1.0 };
      SwissEconomicDataService.cache.snbData.policyRate = 2.0;

      const alerts = SwissEconomicDataService.checkAlertThresholds(userProfile);
      const rateAlert = alerts.find(a => a.type === 'policy_rate_change');

      expect(rateAlert).toBeDefined();
      expect(rateAlert.message).toContain('increased');
    });

    test('should generate high volatility alert', () => {
      SwissEconomicDataService.cache.marketData.volatilityIndex = 35;

      const alerts = SwissEconomicDataService.checkAlertThresholds({});
      const volatilityAlert = alerts.find(a => a.type === 'high_volatility');

      expect(volatilityAlert).toBeDefined();
      expect(volatilityAlert.severity).toBe('warning');
    });

    test('should not generate alerts for normal conditions', () => {
      const userProfile = { lastKnownPolicyRate: 1.75 };

      const alerts = SwissEconomicDataService.checkAlertThresholds(userProfile);

      expect(alerts).toHaveLength(0);
    });

    test('should handle missing data gracefully', () => {
      SwissEconomicDataService.cache.snbData = null;
      SwissEconomicDataService.cache.inflationData = null;

      const alerts = SwissEconomicDataService.checkAlertThresholds({});

      expect(alerts).toHaveLength(0);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    beforeEach(async () => {
      // Populate cache with test data before edge case tests
      await SwissEconomicDataService.getAllEconomicData();
    });

    test('should handle extreme market conditions', () => {
      SwissEconomicDataService.cache.marketData.volatilityIndex = 100;
      SwissEconomicDataService.cache.snbData.currentInflation = 10;
      SwissEconomicDataService.cache.snbData.economicGrowthForecast = -5;

      const returns = SwissEconomicDataService.calculateDynamicReturns();

      expect(returns.stocks).toBeGreaterThanOrEqual(3.0); // Minimum bound
      expect(returns.stocks).toBeLessThanOrEqual(12.0); // Maximum bound
      expect(returns.confidence).toBeGreaterThanOrEqual(50); // Minimum confidence
    });

    test('should handle negative interest rates', () => {
      SwissEconomicDataService.cache.snbData.policyRate = -0.5;
      SwissEconomicDataService.cache.marketData.bondYield10Y = -0.2;

      const returns = SwissEconomicDataService.calculateDynamicReturns();

      expect(returns.bonds).toBeGreaterThanOrEqual(0.5); // Minimum bond return
      expect(returns.cash).toBeLessThan(0); // Can be negative
    });

    test('should validate data consistency', async () => {
      const data = await SwissEconomicDataService.getAllEconomicData();

      expect(data.snb.lastUpdated).toBeDefined();
      expect(data.market.lastUpdated).toBeDefined();
      expect(data.inflation.lastUpdated).toBeDefined();

      expect(new Date(data.snb.lastUpdated)).toBeInstanceOf(Date);
      expect(new Date(data.market.lastUpdated)).toBeInstanceOf(Date);
      expect(new Date(data.inflation.lastUpdated)).toBeInstanceOf(Date);
    });
  });
});

// Export for use in other test files
export { SwissEconomicDataService };
