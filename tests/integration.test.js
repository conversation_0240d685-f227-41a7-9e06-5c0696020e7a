/**
 * Integration Tests for Swiss Budget Pro
 * Tests the interaction between different systems and end-to-end functionality
 */

// Import test modules
import { SwissEconomicDataService } from './economic-data-service.test.js';
import { SwissTaxEngine } from './swiss-tax-engine.test.js';

// Mock complete Swiss Budget Pro system
const SwissBudgetPro = {
  // State management
  state: {
    selectedCanton: 'ZH',
    civilStatus: 'single',
    hasSecondPillar: true,
    currentPillar3a: 50000,
    useDynamicReturns: true,
    monthlyIncome: 10000,
    currentAge: 30,
    retirementAge: 65,
    currentSavings: 100000,
    netWorth: 500000
  },

  // Calculate complete financial analysis
  async calculateCompleteAnalysis() {
    const annualIncome = this.state.monthlyIncome * 12;

    // Tax analysis
    const userProfile = {
      income: annualIncome,
      netWorth: this.state.netWorth,
      canton: this.state.selectedCanton,
      civilStatus: this.state.civilStatus,
      currentAge: this.state.currentAge,
      retirementAge: this.state.retirementAge,
      hasSecondPillar: this.state.hasSecondPillar
    };

    const taxAnalysis = {
      currentTax: SwissTaxEngine.calculateTotalTax(
        annualIncome,
        this.state.netWorth,
        this.state.selectedCanton,
        this.state.civilStatus
      ),
      pillar3aOptimization: SwissTaxEngine.optimizePillar3a(userProfile),
      cantonalComparison: SwissTaxEngine.findOptimalCanton(userProfile)
    };

    // Economic analysis (await if dynamic)
    const economicAnalysis = this.state.useDynamicReturns
      ? await this.calculateDynamicProjections()
      : this.calculateStaticProjections();

    return {
      taxAnalysis,
      economicAnalysis,
      combinedOptimization: this.calculateCombinedOptimization(taxAnalysis, economicAnalysis)
    };
  },

  async calculateDynamicProjections() {
    const economicData = await SwissEconomicDataService.getAllEconomicData();
    const dynamicReturns = SwissEconomicDataService.calculateDynamicReturns();
    const alerts = SwissEconomicDataService.checkAlertThresholds({
      income: this.state.monthlyIncome * 12,
      lastKnownPolicyRate: 1.75
    });

    return {
      expectedReturn: dynamicReturns.portfolio,
      inflationRate: dynamicReturns.inflation,
      confidence: dynamicReturns.confidence,
      economicData,
      alerts,
      source: 'dynamic'
    };
  },

  calculateStaticProjections() {
    return {
      expectedReturn: 5.0,
      inflationRate: 2.0,
      confidence: 75,
      source: 'static'
    };
  },

  calculateCombinedOptimization(taxAnalysis, economicAnalysis) {
    const optimizations = [];

    // Tax-based optimizations
    if (taxAnalysis.pillar3aOptimization.annualTaxSavings > 1000) {
      optimizations.push({
        type: 'tax_pillar3a',
        priority: 'high',
        annualSavings: taxAnalysis.pillar3aOptimization.annualTaxSavings,
        description: 'Maximize Pillar 3a contributions for tax savings'
      });
    }

    // Economic-based optimizations
    if (economicAnalysis.source === 'dynamic' && economicAnalysis.confidence < 70) {
      optimizations.push({
        type: 'economic_conservative',
        priority: 'medium',
        description: 'Consider conservative approach due to economic uncertainty',
        impact: 'Reduce risk exposure during volatile periods'
      });
    }

    // Combined optimizations
    const bestCanton = taxAnalysis.cantonalComparison[0];
    if (bestCanton.canton !== this.state.selectedCanton && bestCanton.savings > 5000) {
      optimizations.push({
        type: 'relocation',
        priority: 'high',
        annualSavings: bestCanton.savings,
        description: `Consider relocating to ${bestCanton.name} for tax optimization`,
        economicImpact: this.calculateRelocationEconomicImpact(bestCanton, economicAnalysis)
      });
    }

    return optimizations.sort((a, b) => (b.annualSavings || 0) - (a.annualSavings || 0));
  },

  calculateRelocationEconomicImpact(canton, economicAnalysis) {
    // Factor in economic conditions for relocation timing
    // Canton-specific adjustments could be added here
    const cantonPrefix = canton === 'ZG' ? 'Premium location: ' : '';

    if (economicAnalysis.source === 'dynamic') {
      if (economicAnalysis.confidence > 80) {
        return cantonPrefix + 'Favorable economic conditions for relocation';
      } else if (economicAnalysis.confidence < 60) {
        return cantonPrefix + 'Consider delaying relocation due to economic uncertainty';
      }
    }
    return cantonPrefix + 'Neutral economic conditions for relocation';
  },

  // FIRE calculation with integrated optimizations
  async calculateOptimizedFIRE() {
    const analysis = await this.calculateCompleteAnalysis();
    const baseIncome = this.state.monthlyIncome * 12;

    // Apply tax optimizations
    const taxSavings = analysis.taxAnalysis.pillar3aOptimization.annualTaxSavings;
    const netIncome = baseIncome - analysis.taxAnalysis.currentTax.totalTax + taxSavings;

    // Apply economic projections
    const expectedReturn = analysis.economicAnalysis.expectedReturn / 100;
    const inflationRate = analysis.economicAnalysis.inflationRate / 100;
    const realReturn = expectedReturn - inflationRate;

    // Calculate FIRE number and timeline
    const annualExpenses = netIncome * 0.7; // Assume 70% of net income for expenses
    const fireNumber = annualExpenses * 25; // 4% rule
    const currentSavings = this.state.currentSavings + this.state.currentPillar3a;
    const yearsToFIRE = this.calculateYearsToTarget(currentSavings, fireNumber, netIncome * 0.3, realReturn);

    return {
      fireNumber,
      currentSavings,
      yearsToFIRE,
      netIncome,
      taxSavings,
      realReturn: realReturn * 100,
      confidence: analysis.economicAnalysis.confidence,
      optimizations: analysis.combinedOptimization
    };
  },

  calculateYearsToTarget(currentAmount, targetAmount, annualSavings, realReturn) {
    if (annualSavings <= 0) return Infinity;
    if (currentAmount >= targetAmount) return 0;

    const monthlyReturn = realReturn / 12;
    const monthlyPayment = annualSavings / 12;
    const remainingAmount = targetAmount - currentAmount;

    if (monthlyReturn === 0) {
      return remainingAmount / annualSavings;
    }

    const months = Math.log(1 + (remainingAmount * monthlyReturn) / monthlyPayment) / Math.log(1 + monthlyReturn);
    return Math.max(0, months / 12);
  }
};

// Integration Test Suite
describe('Swiss Budget Pro Integration Tests', () => {

  beforeEach(async () => {
    // Reset to default state
    SwissBudgetPro.state = {
      selectedCanton: 'ZH',
      civilStatus: 'single',
      hasSecondPillar: true,
      currentPillar3a: 50000,
      useDynamicReturns: true,
      monthlyIncome: 10000,
      currentAge: 30,
      retirementAge: 65,
      currentSavings: 100000,
      netWorth: 500000
    };

    // Populate economic data cache for integration tests
    await SwissEconomicDataService.getAllEconomicData();
  });

  describe('Tax and Economic Integration', () => {
    test('should integrate tax calculations with economic projections', async () => {
      const analysis = await SwissBudgetPro.calculateCompleteAnalysis();

      expect(analysis).toHaveProperty('taxAnalysis');
      expect(analysis).toHaveProperty('economicAnalysis');
      expect(analysis).toHaveProperty('combinedOptimization');

      expect(analysis.taxAnalysis.currentTax.totalTax).toBeGreaterThan(0);
      expect(analysis.economicAnalysis.expectedReturn).toBeGreaterThan(0);
      expect(Array.isArray(analysis.combinedOptimization)).toBe(true);
    });

    test('should provide different results for dynamic vs static returns', async () => {
      // Test with dynamic returns
      SwissBudgetPro.state.useDynamicReturns = true;
      const dynamicAnalysis = await SwissBudgetPro.calculateCompleteAnalysis();

      // Test with static returns
      SwissBudgetPro.state.useDynamicReturns = false;
      const staticAnalysis = await SwissBudgetPro.calculateCompleteAnalysis();

      expect(dynamicAnalysis.economicAnalysis.source).toBe('dynamic');
      expect(staticAnalysis.economicAnalysis.source).toBe('static');
      expect(dynamicAnalysis.economicAnalysis.expectedReturn).not.toBe(staticAnalysis.economicAnalysis.expectedReturn);
    });

    test('should generate combined optimizations', async () => {
      const analysis = await SwissBudgetPro.calculateCompleteAnalysis();

      expect(analysis.combinedOptimization.length).toBeGreaterThan(0);

      const pillar3aOpt = analysis.combinedOptimization.find(opt => opt.type === 'tax_pillar3a');
      expect(pillar3aOpt).toBeDefined();
      expect(pillar3aOpt.annualSavings).toBeGreaterThan(0);
    });
  });

  describe('Cantonal Optimization Integration', () => {
    test('should recommend relocation when beneficial', async () => {
      // Set up scenario where relocation would be beneficial
      SwissBudgetPro.state.selectedCanton = 'GE'; // High tax canton
      SwissBudgetPro.state.monthlyIncome = 15000; // High income
      SwissBudgetPro.state.netWorth = 1000000; // High net worth

      const analysis = await SwissBudgetPro.calculateCompleteAnalysis();
      const relocationOpt = analysis.combinedOptimization.find(opt => opt.type === 'relocation');

      expect(relocationOpt).toBeDefined();
      expect(relocationOpt.annualSavings).toBeGreaterThan(5000);
      expect(relocationOpt.priority).toBe('high');
    });

    test('should not recommend relocation when not beneficial', async () => {
      // Set up scenario where current canton is optimal
      SwissBudgetPro.state.selectedCanton = 'ZG'; // Low tax canton
      SwissBudgetPro.state.monthlyIncome = 5000; // Lower income

      const analysis = await SwissBudgetPro.calculateCompleteAnalysis();
      const relocationOpt = analysis.combinedOptimization.find(opt => opt.type === 'relocation');

      expect(relocationOpt).toBeUndefined();
    });
  });

  describe('FIRE Calculation Integration', () => {
    test('should calculate optimized FIRE timeline', async () => {
      const fireAnalysis = await SwissBudgetPro.calculateOptimizedFIRE();

      expect(fireAnalysis).toHaveProperty('fireNumber');
      expect(fireAnalysis).toHaveProperty('yearsToFIRE');
      expect(fireAnalysis).toHaveProperty('taxSavings');
      expect(fireAnalysis).toHaveProperty('realReturn');
      expect(fireAnalysis).toHaveProperty('optimizations');

      expect(fireAnalysis.fireNumber).toBeGreaterThan(0);
      expect(fireAnalysis.yearsToFIRE).toBeGreaterThan(0);
      expect(fireAnalysis.taxSavings).toBeGreaterThan(0);
    });

    test('should show impact of tax optimizations on FIRE timeline', async () => {
      // Calculate without optimizations (simulate no Pillar 3a)
      const originalPillar3a = SwissBudgetPro.state.currentPillar3a;
      SwissBudgetPro.state.currentPillar3a = 0;

      const baselineAnalysis = await SwissBudgetPro.calculateOptimizedFIRE();

      // Calculate with optimizations
      SwissBudgetPro.state.currentPillar3a = originalPillar3a;
      const optimizedAnalysis = await SwissBudgetPro.calculateOptimizedFIRE();

      expect(optimizedAnalysis.yearsToFIRE).toBeLessThan(baselineAnalysis.yearsToFIRE);
      expect(optimizedAnalysis.taxSavings).toBeGreaterThan(0);
    });

    test('should adjust timeline based on economic conditions', async () => {
      // Test with high confidence economic conditions
      SwissBudgetPro.state.useDynamicReturns = true;
      const analysis = await SwissBudgetPro.calculateOptimizedFIRE();

      expect(analysis.confidence).toBeGreaterThan(0);
      expect(analysis.realReturn).toBeGreaterThan(0);

      // FIRE timeline should be reasonable (not infinite or negative)
      expect(analysis.yearsToFIRE).toBeGreaterThan(0);
      expect(analysis.yearsToFIRE).toBeLessThan(50);
    });
  });

  describe('Economic Alert Integration', () => {
    test('should generate conservative recommendations during high volatility', async () => {
      // Ensure cache is populated first
      await SwissEconomicDataService.getAllEconomicData();

      // Mock high volatility scenario
      SwissEconomicDataService.cache.marketData.volatilityIndex = 35;
      SwissEconomicDataService.cache.marketData.smiChange = -2.5;
      SwissEconomicDataService.cache.marketData.spiYearReturn = -5.0;

      const analysis = await SwissBudgetPro.calculateCompleteAnalysis();

      expect(analysis.economicAnalysis).toHaveProperty('alerts');
      expect(analysis.economicAnalysis).toHaveProperty('confidence');
      expect(analysis.economicAnalysis.confidence).toBeLessThanOrEqual(80);
    });

    test('should integrate inflation alerts with tax planning', async () => {
      // Ensure cache is populated first
      await SwissEconomicDataService.getAllEconomicData();

      // Mock high inflation scenario
      SwissEconomicDataService.cache.inflationData.currentCPI = 3.5;
      SwissEconomicDataService.cache.inflationData.coreCPI = 3.2;

      const analysis = await SwissBudgetPro.calculateCompleteAnalysis();

      expect(analysis.economicAnalysis).toHaveProperty('alerts');
      expect(Array.isArray(analysis.economicAnalysis.alerts)).toBe(true);

      // Should still recommend tax optimizations even during high inflation
      const pillar3aOpt = analysis.combinedOptimization.find(opt => opt.type === 'tax_pillar3a');
      expect(pillar3aOpt).toBeDefined();
    });
  });

  describe('Data Consistency and Validation', () => {
    test('should maintain data consistency across calculations', async () => {
      const analysis = await SwissBudgetPro.calculateCompleteAnalysis();
      const fireAnalysis = await SwissBudgetPro.calculateOptimizedFIRE();

      // Tax calculations should be consistent
      expect(analysis.taxAnalysis.currentTax.totalTax).toBeGreaterThan(0);
      expect(fireAnalysis.taxSavings).toBe(analysis.taxAnalysis.pillar3aOptimization.annualTaxSavings);

      // Economic projections should be present and valid
      expect(analysis.economicAnalysis.expectedReturn).toBeGreaterThan(0);
      expect(analysis.economicAnalysis.inflationRate).toBeGreaterThan(0);
      expect(fireAnalysis.realReturn).toBeGreaterThan(0);
    });

    test('should handle edge cases gracefully', async () => {
      // Test with extreme values
      SwissBudgetPro.state.monthlyIncome = 0;
      SwissBudgetPro.state.currentSavings = 0;
      SwissBudgetPro.state.netWorth = 0;

      const analysis = await SwissBudgetPro.calculateCompleteAnalysis();
      const fireAnalysis = await SwissBudgetPro.calculateOptimizedFIRE();

      expect(analysis.taxAnalysis.currentTax.totalTax).toBe(0);
      expect(fireAnalysis.yearsToFIRE).toBe(Infinity);
    });

    test('should validate all required properties are present', async () => {
      const analysis = await SwissBudgetPro.calculateCompleteAnalysis();

      // Validate tax analysis structure
      expect(analysis.taxAnalysis.currentTax).toHaveProperty('federalTax');
      expect(analysis.taxAnalysis.currentTax).toHaveProperty('cantonalTax');
      expect(analysis.taxAnalysis.currentTax).toHaveProperty('wealthTax');
      expect(analysis.taxAnalysis.currentTax).toHaveProperty('totalTax');
      expect(analysis.taxAnalysis.currentTax).toHaveProperty('effectiveRate');

      // Validate economic analysis structure
      expect(analysis.economicAnalysis).toHaveProperty('expectedReturn');
      expect(analysis.economicAnalysis).toHaveProperty('inflationRate');
      expect(analysis.economicAnalysis).toHaveProperty('confidence');
      expect(analysis.economicAnalysis).toHaveProperty('source');

      // Validate optimization structure
      expect(Array.isArray(analysis.combinedOptimization)).toBe(true);
      if (analysis.combinedOptimization.length > 0) {
        analysis.combinedOptimization.forEach(opt => {
          expect(opt).toHaveProperty('type');
          expect(opt).toHaveProperty('priority');
          expect(opt).toHaveProperty('description');
        });
      }
    });
  });
});

// Export for use in other test files
export { SwissBudgetPro };
