import { Page, expect } from '@playwright/test';

/**
 * Enhanced Test Helper Utilities for Swiss Budget Pro E2E Tests
 * Includes comprehensive logging and debugging capabilities
 */

export class TestHelpers {
  private logs: Array<{ timestamp: string; level: string; message: string; data?: any }> = [];
  private consoleMessages: string[] = [];
  private networkErrors: string[] = [];
  private networkRequests: Array<{ url: string; method: string; status?: number; timestamp: string }> = [];

  constructor(private page: Page) {
    this.setupLogging();
  }

  /**
   * Setup comprehensive logging for the page
   */
  private setupLogging(): void {
    // Capture console messages
    this.page.on('console', msg => {
      const message = `${msg.type()}: ${msg.text()}`;
      this.consoleMessages.push(message);
      this.log('console', message, { type: msg.type(), location: msg.location() });
    });

    // Capture network failures
    this.page.on('requestfailed', request => {
      const error = `Failed: ${request.url()} - ${request.failure()?.errorText}`;
      this.networkErrors.push(error);
      this.log('network-error', error, { url: request.url(), method: request.method() });
    });

    // Capture all network requests
    this.page.on('request', request => {
      this.networkRequests.push({
        url: request.url(),
        method: request.method(),
        timestamp: new Date().toISOString()
      });
    });

    // Capture network responses
    this.page.on('response', response => {
      const request = this.networkRequests.find(req => req.url === response.url());
      if (request) {
        request.status = response.status();
      }

      if (!response.ok()) {
        this.log('network-error', `HTTP ${response.status()}: ${response.url()}`, {
          status: response.status(),
          url: response.url()
        });
      }
    });

    // Capture page errors
    this.page.on('pageerror', error => {
      this.log('page-error', `Page Error: ${error.message}`, {
        name: error.name,
        stack: error.stack
      });
    });
  }

  /**
   * Internal logging method
   */
  private log(level: string, message: string, data?: any): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      data
    };
    this.logs.push(logEntry);

    // Also log to console for immediate visibility
    console.log(`[${level.toUpperCase()}] ${message}`, data || '');
  }

  /**
   * Wait for network requests to complete
   */
  async waitForNetworkIdle(timeout: number = 5000): Promise<void> {
    this.log('info', `Waiting for network idle (timeout: ${timeout}ms)`);
    try {
      await this.page.waitForLoadState('networkidle', { timeout });
      this.log('success', 'Network idle achieved');
    } catch (error) {
      this.log('warning', `Network idle timeout after ${timeout}ms`, { error: error.message });
      throw error;
    }
  }

  /**
   * Wait for all animations to complete
   */
  async waitForAnimations(): Promise<void> {
    await this.page.waitForFunction(() => {
      return document.getAnimations().length === 0;
    }, { timeout: 5000 });
  }

  /**
   * Take a full page screenshot with timestamp and logging
   */
  async takeTimestampedScreenshot(name: string): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const screenshotPath = `test-results/screenshots/${name}-${timestamp}.png`;

    this.log('info', `Taking screenshot: ${name}`, { path: screenshotPath });

    try {
      await this.page.screenshot({
        path: screenshotPath,
        fullPage: true
      });
      this.log('success', `Screenshot saved: ${screenshotPath}`);
    } catch (error) {
      this.log('error', `Failed to take screenshot: ${error.message}`, { path: screenshotPath });
      throw error;
    }
  }

  /**
   * Wait for Swiss Budget Pro to fully load with comprehensive logging
   */
  async waitForSwissBudgetProLoad(timeout: number = 30000): Promise<void> {
    this.log('info', `Waiting for Swiss Budget Pro to load (timeout: ${timeout}ms)`);

    try {
      // Wait for the main heading
      this.log('info', 'Waiting for main heading...');
      await this.page.waitForSelector('h1', { timeout: 15000 });
      this.log('success', 'Main heading found');

      // Wait for Swiss Budget Pro text
      this.log('info', 'Waiting for Swiss Budget Pro text...');
      await this.page.waitForFunction(() => {
        return document.body.textContent?.includes('Swiss Budget Pro') ||
               document.body.textContent?.includes('Your Advanced Financial Command Center');
      }, { timeout: 15000 });
      this.log('success', 'Swiss Budget Pro text found');

      // Wait for React to render
      this.log('info', 'Waiting for React root...');
      await this.page.waitForSelector('#root', { timeout: 10000 });
      this.log('success', 'React root found');

      // Check for error boundary
      const errorBoundary = await this.page.locator('[data-testid="error-boundary"]').count();
      if (errorBoundary > 0) {
        const errorMessage = await this.page.getAttribute('[data-testid="error-boundary"]', 'data-error-message');
        this.log('error', `Error boundary detected: ${errorMessage}`);
        throw new Error(`Application error detected: ${errorMessage}`);
      }

      // Wait for loading spinners to disappear
      this.log('info', 'Waiting for loading spinners to disappear...');
      await this.page.waitForFunction(() => {
        const spinners = document.querySelectorAll('[data-testid="loading-spinner"]');
        return Array.from(spinners).every(spinner =>
          spinner.getAttribute('style')?.includes('display: none') ||
          !spinner.isConnected
        );
      }, { timeout: 5000 }).catch(() => {
        this.log('warning', 'Loading spinner timeout - continuing anyway');
      });

      this.log('success', 'Swiss Budget Pro loaded successfully');
    } catch (error) {
      this.log('error', `Failed to load Swiss Budget Pro: ${error.message}`);
      await this.captureDebugInfo('load-failure');
      throw error;
    }
  }

  /**
   * Capture comprehensive debug information
   */
  async captureDebugInfo(context: string): Promise<void> {
    this.log('info', `Capturing debug info for context: ${context}`);

    try {
      // Take screenshot
      await this.takeTimestampedScreenshot(`debug-${context}`);

      // Get page content
      const content = await this.page.content();
      this.log('debug', 'Page HTML content captured', {
        contentLength: content.length,
        hasSwissBudgetPro: content.includes('Swiss Budget Pro'),
        hasErrorBoundary: content.includes('data-testid="error-boundary"')
      });

      // Get page title
      const title = await this.page.title();
      this.log('debug', `Page title: ${title}`);

      // Check for React errors
      const reactErrors = await this.page.evaluate(() => {
        return (window as any).lastError || null;
      });

      if (reactErrors) {
        this.log('error', 'React error detected', reactErrors);
      }

      // Get localStorage state
      const localStorage = await this.page.evaluate(() => {
        const storage: Record<string, string> = {};
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key) {
            storage[key] = localStorage.getItem(key) || '';
          }
        }
        return storage;
      });

      this.log('debug', 'LocalStorage state captured', {
        keys: Object.keys(localStorage).length,
        swissBudgetProKeys: Object.keys(localStorage).filter(k => k.startsWith('swissBudgetPro_')).length
      });

    } catch (error) {
      this.log('error', `Failed to capture debug info: ${error.message}`);
    }
  }

  /**
   * Get all captured logs
   */
  getLogs(): Array<{ timestamp: string; level: string; message: string; data?: any }> {
    return [...this.logs];
  }

  /**
   * Get console messages
   */
  getConsoleMessages(): string[] {
    return [...this.consoleMessages];
  }

  /**
   * Get network errors
   */
  getNetworkErrors(): string[] {
    return [...this.networkErrors];
  }

  /**
   * Get network requests
   */
  getNetworkRequests(): Array<{ url: string; method: string; status?: number; timestamp: string }> {
    return [...this.networkRequests];
  }

  /**
   * Generate a comprehensive test report
   */
  async generateTestReport(testName: string): Promise<void> {
    const report = {
      testName,
      timestamp: new Date().toISOString(),
      logs: this.getLogs(),
      consoleMessages: this.getConsoleMessages(),
      networkErrors: this.getNetworkErrors(),
      networkRequests: this.getNetworkRequests(),
      pageInfo: {
        url: this.page.url(),
        title: await this.page.title().catch(() => 'Unknown'),
        viewport: await this.page.viewportSize()
      }
    };

    const reportPath = `test-results/reports/${testName}-${Date.now()}.json`;

    try {
      const fs = require('fs');
      const path = require('path');

      // Ensure directory exists
      const dir = path.dirname(reportPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      this.log('success', `Test report generated: ${reportPath}`);
    } catch (error) {
      this.log('error', `Failed to generate test report: ${error.message}`);
    }
  }

  /**
   * Simulate slow network conditions
   */
  async simulateSlowNetwork(): Promise<void> {
    const client = await this.page.context().newCDPSession(this.page);
    await client.send('Network.emulateNetworkConditions', {
      offline: false,
      downloadThroughput: 500 * 1024, // 500kb/s
      uploadThroughput: 500 * 1024,
      latency: 100
    });
  }

  /**
   * Reset network conditions to normal
   */
  async resetNetworkConditions(): Promise<void> {
    const client = await this.page.context().newCDPSession(this.page);
    await client.send('Network.emulateNetworkConditions', {
      offline: false,
      downloadThroughput: -1,
      uploadThroughput: -1,
      latency: 0
    });
  }

  /**
   * Check for console errors
   */
  async expectNoConsoleErrors(): Promise<void> {
    const errors: string[] = [];

    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });

    // Wait a bit for any errors to appear
    await this.page.waitForTimeout(1000);

    expect(errors).toHaveLength(0);
  }

  /**
   * Measure Core Web Vitals
   */
  async measureCoreWebVitals(): Promise<{
    lcp: number;
    fid: number;
    cls: number;
  }> {
    return await this.page.evaluate(() => {
      return new Promise((resolve) => {
        const vitals = { lcp: 0, fid: 0, cls: 0 };

        // Largest Contentful Paint
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          vitals.lcp = lastEntry.startTime;
        }).observe({ entryTypes: ['largest-contentful-paint'] });

        // Cumulative Layout Shift
        new PerformanceObserver((list) => {
          let cls = 0;
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              cls += (entry as any).value;
            }
          }
          vitals.cls = cls;
        }).observe({ entryTypes: ['layout-shift'] });

        // First Input Delay (approximation)
        new PerformanceObserver((list) => {
          const firstInput = list.getEntries()[0];
          if (firstInput) {
            vitals.fid = (firstInput as any).processingStart - firstInput.startTime;
          }
        }).observe({ entryTypes: ['first-input'] });

        // Resolve after a short delay to collect metrics
        setTimeout(() => resolve(vitals), 3000);
      });
    });
  }

  /**
   * Generate random Swiss financial data
   */
  generateRandomFinancialData(): {
    income: number;
    age: number;
    canton: string;
    expenses: number;
  } {
    const cantons = ['ZH', 'BE', 'LU', 'VD', 'GE', 'BS', 'ZG'];

    return {
      income: Math.floor(Math.random() * 10000) + 5000, // 5k-15k CHF
      age: Math.floor(Math.random() * 30) + 25, // 25-55 years
      canton: cantons[Math.floor(Math.random() * cantons.length)],
      expenses: Math.floor(Math.random() * 5000) + 2000 // 2k-7k CHF
    };
  }

  /**
   * Format Swiss currency for comparison
   */
  formatSwissCurrency(amount: number): string {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF'
    }).format(amount);
  }

  /**
   * Parse Swiss currency string to number
   */
  parseSwissCurrency(currencyString: string): number {
    const cleaned = currencyString.replace(/[CHF\s']/g, '');
    return parseFloat(cleaned);
  }

  /**
   * Wait for element to be stable (not moving)
   */
  async waitForElementStable(selector: string, timeout: number = 5000): Promise<void> {
    const element = this.page.locator(selector);
    await element.waitFor({ state: 'visible' });

    let previousBox = await element.boundingBox();
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      await this.page.waitForTimeout(100);
      const currentBox = await element.boundingBox();

      if (previousBox && currentBox &&
          previousBox.x === currentBox.x &&
          previousBox.y === currentBox.y &&
          previousBox.width === currentBox.width &&
          previousBox.height === currentBox.height) {
        return; // Element is stable
      }

      previousBox = currentBox;
    }
  }

  /**
   * Simulate user typing with realistic delays
   */
  async typeWithDelay(selector: string, text: string, delay: number = 100): Promise<void> {
    const element = this.page.locator(selector);
    await element.clear();

    for (const char of text) {
      await element.type(char);
      await this.page.waitForTimeout(delay);
    }
  }

  /**
   * Check if element is in viewport
   */
  async isElementInViewport(selector: string): Promise<boolean> {
    return await this.page.locator(selector).evaluate((element) => {
      const rect = element.getBoundingClientRect();
      return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
      );
    });
  }

  /**
   * Scroll element into view smoothly
   */
  async scrollIntoView(selector: string): Promise<void> {
    await this.page.locator(selector).evaluate((element) => {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    });

    // Wait for scroll to complete
    await this.page.waitForTimeout(500);
  }

  /**
   * Get computed style property
   */
  async getComputedStyle(selector: string, property: string): Promise<string> {
    return await this.page.locator(selector).evaluate((element, prop) => {
      return window.getComputedStyle(element).getPropertyValue(prop);
    }, property);
  }

  /**
   * Verify responsive design at different breakpoints
   */
  async testResponsiveBreakpoints(selector: string): Promise<void> {
    const breakpoints = [
      { name: 'mobile', width: 375, height: 667 },
      { name: 'tablet', width: 768, height: 1024 },
      { name: 'desktop', width: 1280, height: 720 },
      { name: 'large', width: 1920, height: 1080 }
    ];

    for (const breakpoint of breakpoints) {
      await this.page.setViewportSize({
        width: breakpoint.width,
        height: breakpoint.height
      });

      await this.page.waitForTimeout(300); // Allow layout to settle

      // Verify element is visible and properly positioned
      await expect(this.page.locator(selector)).toBeVisible();

      // Take screenshot for visual verification
      await this.page.screenshot({
        path: `test-results/responsive/${breakpoint.name}-${Date.now()}.png`,
        fullPage: true
      });
    }
  }

  /**
   * Mock API responses for testing
   */
  async mockApiResponse(url: string, response: any): Promise<void> {
    await this.page.route(url, async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(response)
      });
    });
  }

  /**
   * Simulate network failure
   */
  async simulateNetworkFailure(url: string): Promise<void> {
    await this.page.route(url, async (route) => {
      await route.abort('failed');
    });
  }

  /**
   * Clear all browser data
   */
  async clearBrowserData(): Promise<void> {
    await this.page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();

      // Clear cookies
      document.cookie.split(";").forEach((c) => {
        const eqPos = c.indexOf("=");
        const name = eqPos > -1 ? c.substr(0, eqPos) : c;
        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
      });
    });
  }

  /**
   * Wait for specific text to appear
   */
  async waitForText(text: string, timeout: number = 10000): Promise<void> {
    await this.page.waitForFunction(
      (searchText) => document.body.textContent?.includes(searchText),
      text,
      { timeout }
    );
  }

  /**
   * Get page performance metrics
   */
  async getPerformanceMetrics(): Promise<any> {
    return await this.page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
        firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
      };
    });
  }
}
