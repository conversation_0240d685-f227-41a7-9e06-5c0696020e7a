import { expect, test } from '@playwright/test';
import { DashboardPage } from '../../pages/dashboard-page';
import { swissTestScenarios } from '../../fixtures/swiss-scenarios';

test.describe('Performance Benchmarks', () => {
  let dashboardPage: DashboardPage;

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
  });

  test('Page load performance', async ({ page }) => {
    console.log('⚡ Testing page load performance');
    
    const startTime = Date.now();
    
    await dashboardPage.goto();
    await dashboardPage.waitForPageLoad();
    
    const loadTime = Date.now() - startTime;
    
    // Page should load in under 3 seconds
    expect(loadTime).toBeLessThan(3000);
    console.log(`✅ Page loaded in ${loadTime}ms`);
    
    // Verify critical elements are visible
    await expect(dashboardPage.overviewTab).toBeVisible();
    await expect(dashboardPage.monthlyIncomeInput).toBeVisible();
  });

  test('Calculation performance', async ({ page }) => {
    console.log('🧮 Testing calculation performance');
    
    await dashboardPage.goto();
    await dashboardPage.waitForPageLoad();
    
    const scenario = swissTestScenarios.zurichProfessional;
    
    const startTime = Date.now();
    
    // Input scenario data
    await dashboardPage.inputScenario(scenario);
    
    const calculationTime = Date.now() - startTime;
    
    // Calculations should complete in under 2 seconds
    expect(calculationTime).toBeLessThan(2000);
    console.log(`✅ Calculations completed in ${calculationTime}ms`);
    
    // Verify results are displayed
    await expect(dashboardPage.fireProjection).toBeVisible();
    await expect(dashboardPage.monthlyTaxAmount).toBeVisible();
  });

  test('Tab switching performance', async ({ page }) => {
    console.log('🔄 Testing tab switching performance');
    
    await dashboardPage.goto();
    await dashboardPage.waitForPageLoad();
    
    const scenario = swissTestScenarios.genevaExecutive;
    await dashboardPage.inputScenario(scenario);
    
    const tabs = [
      { name: 'Overview', method: () => dashboardPage.navigateToOverviewTab() },
      { name: 'Budget', method: () => dashboardPage.navigateToBudgetTab() },
      { name: 'Target', method: () => dashboardPage.navigateToTargetTab() },
      { name: 'Tax Optimization', method: () => dashboardPage.navigateToTaxOptimizationTab() },
      { name: 'Economic Data', method: () => dashboardPage.navigateToEconomicDataTab() },
      { name: 'Swiss Relocation', method: () => dashboardPage.navigateToSwissRelocationTab() },
      { name: 'FIRE Acceleration', method: () => dashboardPage.navigateToFireAccelerationTab() }
    ];
    
    for (const tab of tabs) {
      const startTime = Date.now();
      
      await tab.method();
      
      const switchTime = Date.now() - startTime;
      
      // Tab switching should be fast (under 500ms)
      expect(switchTime).toBeLessThan(500);
      console.log(`✅ ${tab.name} tab loaded in ${switchTime}ms`);
    }
  });

  test('Large dataset performance', async ({ page }) => {
    console.log('📊 Testing large dataset performance');
    
    await dashboardPage.goto();
    await dashboardPage.waitForPageLoad();
    
    // Test with high income scenario (more complex calculations)
    const highIncomeScenario = {
      ...swissTestScenarios.genevaExecutive,
      income: {
        monthly: 50000, // Very high income
        bonus: 200000,
        pillar3a: 14112
      }
    };
    
    const startTime = Date.now();
    
    await dashboardPage.inputScenario(highIncomeScenario);
    
    // Navigate to complex tabs that require heavy calculations
    await dashboardPage.navigateToSwissRelocationTab();
    await dashboardPage.navigateToFireAccelerationTab();
    
    const processingTime = Date.now() - startTime;
    
    // Even complex calculations should complete in under 5 seconds
    expect(processingTime).toBeLessThan(5000);
    console.log(`✅ Large dataset processed in ${processingTime}ms`);
  });

  test('Memory usage stability', async ({ page }) => {
    console.log('🧠 Testing memory usage stability');
    
    await dashboardPage.goto();
    await dashboardPage.waitForPageLoad();
    
    const scenario = swissTestScenarios.baselTechWorker;
    
    // Perform multiple operations to test memory stability
    for (let i = 0; i < 5; i++) {
      await dashboardPage.inputScenario(scenario);
      
      // Navigate through all tabs
      await dashboardPage.navigateToOverviewTab();
      await dashboardPage.navigateToBudgetTab();
      await dashboardPage.navigateToTaxOptimizationTab();
      await dashboardPage.navigateToSwissRelocationTab();
      
      // Modify income to trigger recalculations
      await dashboardPage.enterMonthlyIncome(scenario.income.monthly + (i * 1000));
      await dashboardPage.waitForCalculations();
      
      console.log(`✅ Iteration ${i + 1} completed`);
    }
    
    // Page should still be responsive
    await expect(dashboardPage.monthlyIncomeInput).toBeVisible();
    await expect(dashboardPage.fireProjection).toBeVisible();
    
    console.log('✅ Memory usage appears stable');
  });

  test('Network performance simulation', async ({ page, context }) => {
    console.log('🌐 Testing network performance simulation');
    
    // Simulate slow 3G connection
    await context.route('**/*', async (route) => {
      await new Promise(resolve => setTimeout(resolve, 100)); // Add 100ms delay
      await route.continue();
    });
    
    const startTime = Date.now();
    
    await dashboardPage.goto();
    await dashboardPage.waitForPageLoad();
    
    const loadTime = Date.now() - startTime;
    
    // Should still load reasonably fast even with network delay
    expect(loadTime).toBeLessThan(5000);
    console.log(`✅ Page loaded with network delay in ${loadTime}ms`);
    
    // Test calculations with network delay
    const scenario = swissTestScenarios.bernConservative;
    
    const calcStartTime = Date.now();
    await dashboardPage.inputScenario(scenario);
    const calcTime = Date.now() - calcStartTime;
    
    // Calculations should still be fast (they're client-side)
    expect(calcTime).toBeLessThan(3000);
    console.log(`✅ Calculations with network delay completed in ${calcTime}ms`);
  });

  test('Concurrent user simulation', async ({ browser }) => {
    console.log('👥 Testing concurrent user simulation');
    
    const contexts = [];
    const dashboards = [];
    
    // Create 3 concurrent user sessions
    for (let i = 0; i < 3; i++) {
      const context = await browser.newContext();
      const page = await context.newPage();
      const dashboard = new DashboardPage(page);
      
      contexts.push(context);
      dashboards.push(dashboard);
    }
    
    // Start all sessions simultaneously
    const startTime = Date.now();
    
    const promises = dashboards.map(async (dashboard, index) => {
      await dashboard.goto();
      await dashboard.waitForPageLoad();
      
      const scenarios = [
        swissTestScenarios.zurichProfessional,
        swissTestScenarios.vaudFamily,
        swissTestScenarios.genevaExecutive
      ];
      
      await dashboard.inputScenario(scenarios[index]);
      
      // Navigate through different tabs for each user
      if (index === 0) {
        await dashboard.navigateToTaxOptimizationTab();
      } else if (index === 1) {
        await dashboard.navigateToSwissRelocationTab();
      } else {
        await dashboard.navigateToFireAccelerationTab();
      }
      
      return dashboard;
    });
    
    await Promise.all(promises);
    
    const totalTime = Date.now() - startTime;
    
    // All sessions should complete in reasonable time
    expect(totalTime).toBeLessThan(10000);
    console.log(`✅ ${dashboards.length} concurrent sessions completed in ${totalTime}ms`);
    
    // Verify all sessions are working
    for (const dashboard of dashboards) {
      await expect(dashboard.fireProjection).toBeVisible();
    }
    
    // Clean up
    for (const context of contexts) {
      await context.close();
    }
  });

  test('Chart rendering performance', async ({ page }) => {
    console.log('📈 Testing chart rendering performance');
    
    await dashboardPage.goto();
    await dashboardPage.waitForPageLoad();
    
    const scenario = swissTestScenarios.zurichProfessional;
    await dashboardPage.inputScenario(scenario);
    
    // Navigate to tabs with charts
    const chartTabs = [
      { name: 'Projections', method: () => dashboardPage.navigateToProjectionsTab() },
      { name: 'Viz', method: () => dashboardPage.navigateToVizTab() },
      { name: 'Interactive Projection', method: () => dashboardPage.navigateToInteractiveProjectionTab() }
    ];
    
    for (const tab of chartTabs) {
      const startTime = Date.now();
      
      await tab.method();
      
      // Wait for charts to render
      await page.waitForSelector('svg, canvas', { timeout: 5000 });
      
      const renderTime = Date.now() - startTime;
      
      // Chart rendering should be fast
      expect(renderTime).toBeLessThan(2000);
      console.log(`✅ ${tab.name} charts rendered in ${renderTime}ms`);
    }
  });

  test('Mobile performance', async ({ browser }) => {
    console.log('📱 Testing mobile performance');
    
    // Create mobile context
    const mobileContext = await browser.newContext({
      ...browser.devices()['iPhone 12'],
      // Simulate slower mobile CPU
      slowMo: 50
    });
    
    const mobilePage = await mobileContext.newPage();
    const mobileDashboard = new DashboardPage(mobilePage);
    
    const startTime = Date.now();
    
    await mobileDashboard.goto();
    await mobileDashboard.waitForPageLoad();
    
    const mobileLoadTime = Date.now() - startTime;
    
    // Mobile should still load in reasonable time
    expect(mobileLoadTime).toBeLessThan(5000);
    console.log(`✅ Mobile page loaded in ${mobileLoadTime}ms`);
    
    // Test mobile calculations
    const scenario = swissTestScenarios.bernConservative;
    
    const calcStartTime = Date.now();
    await mobileDashboard.inputScenario(scenario);
    const mobileCalcTime = Date.now() - calcStartTime;
    
    // Mobile calculations should complete in reasonable time
    expect(mobileCalcTime).toBeLessThan(4000);
    console.log(`✅ Mobile calculations completed in ${mobileCalcTime}ms`);
    
    await mobileContext.close();
  });
});
