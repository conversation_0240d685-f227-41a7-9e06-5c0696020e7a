import { expect, test } from "@playwright/test";
import { DashboardPage } from "../../pages/dashboard-page";
import { HealthcareOptimizerPage } from "../../pages/healthcare-optimizer-page";
import { healthcareTestScenarios, performanceTestScenarios } from "../../fixtures/healthcare-scenarios";

test.describe("Healthcare Cost Optimizer - Performance Tests", () => {
  let dashboardPage: DashboardPage;
  let healthcarePage: HealthcareOptimizerPage;

  test.describe.configure({ mode: "serial", timeout: 120000 });

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
    healthcarePage = new HealthcareOptimizerPage(page);
    await dashboardPage.goto();
    await dashboardPage.clearLocalStorage();
  });

  test("Healthcare tab load performance", async ({ page }) => {
    await test.step("Measure initial page load", async () => {
      const startTime = Date.now();
      await dashboardPage.clickTab("healthcare");
      await page.waitForSelector('h1:has-text("Swiss Healthcare Cost Optimizer")');
      const loadTime = Date.now() - startTime;
      
      expect(loadTime).toBeLessThan(3000); // Should load in under 3 seconds
      console.log(`Healthcare tab loaded in ${loadTime}ms`);
    });

    await test.step("Measure form rendering performance", async () => {
      const startTime = Date.now();
      await page.waitForSelector('input[type="number"]');
      await page.waitForSelector('select');
      const renderTime = Date.now() - startTime;
      
      expect(renderTime).toBeLessThan(1000); // Form should render in under 1 second
      console.log(`Healthcare form rendered in ${renderTime}ms`);
    });
  });

  test("Rapid calculation performance", async ({ page }) => {
    await dashboardPage.clickTab("healthcare");

    for (const [scenarioName, scenario] of Object.entries(healthcareTestScenarios)) {
      await test.step(`Test calculation speed for ${scenarioName}`, async () => {
        const startTime = Date.now();
        
        await healthcarePage.fillHealthProfile(scenario.profile);
        await healthcarePage.goToOptimization();
        await page.waitForSelector('[data-testid="optimization-results"]');
        
        const calculationTime = Date.now() - startTime;
        expect(calculationTime).toBeLessThan(performanceTestScenarios.rapidCalculation.maxCalculationTime);
        
        console.log(`${scenarioName} calculated in ${calculationTime}ms`);
      });
    }
  });

  test("Canton switching performance", async ({ page }) => {
    await dashboardPage.clickTab("healthcare");
    
    // Fill initial profile
    await healthcarePage.fillHealthProfile(performanceTestScenarios.cantonSwitching.baseProfile);

    for (const canton of performanceTestScenarios.cantonSwitching.cantons) {
      await test.step(`Test canton switch to ${canton}`, async () => {
        const startTime = Date.now();
        
        await healthcarePage.selectCanton(canton);
        await healthcarePage.waitForCalculations();
        await healthcarePage.goToOptimization();
        await page.waitForSelector('[data-testid="optimization-results"]');
        
        const switchTime = Date.now() - startTime;
        expect(switchTime).toBeLessThan(performanceTestScenarios.cantonSwitching.maxSwitchTime);
        
        console.log(`Canton switch to ${canton} completed in ${switchTime}ms`);
        
        // Go back to profile for next test
        await page.click('button:has-text("Health Profile")');
      });
    }
  });

  test("Memory usage and resource optimization", async ({ page }) => {
    await dashboardPage.clickTab("healthcare");

    await test.step("Monitor memory usage during intensive operations", async () => {
      // Get initial memory metrics
      const initialMetrics = await page.evaluate(() => {
        return {
          usedJSHeapSize: (performance as any).memory?.usedJSHeapSize || 0,
          totalJSHeapSize: (performance as any).memory?.totalJSHeapSize || 0
        };
      });

      // Perform multiple calculations
      for (let i = 0; i < 5; i++) {
        const scenario = Object.values(healthcareTestScenarios)[i % Object.values(healthcareTestScenarios).length];
        await healthcarePage.fillHealthProfile(scenario.profile);
        await healthcarePage.goToOptimization();
        await healthcarePage.goToFireIntegration();
        await page.click('button:has-text("Health Profile")');
      }

      // Get final memory metrics
      const finalMetrics = await page.evaluate(() => {
        return {
          usedJSHeapSize: (performance as any).memory?.usedJSHeapSize || 0,
          totalJSHeapSize: (performance as any).memory?.totalJSHeapSize || 0
        };
      });

      // Memory usage should not increase dramatically
      const memoryIncrease = finalMetrics.usedJSHeapSize - initialMetrics.usedJSHeapSize;
      const memoryIncreasePercent = (memoryIncrease / initialMetrics.usedJSHeapSize) * 100;
      
      expect(memoryIncreasePercent).toBeLessThan(50); // Memory should not increase by more than 50%
      console.log(`Memory increase: ${memoryIncreasePercent.toFixed(2)}%`);
    });
  });

  test("Concurrent user simulation", async ({ page, context }) => {
    await test.step("Simulate multiple concurrent operations", async () => {
      await dashboardPage.clickTab("healthcare");
      
      // Create multiple promises for concurrent operations
      const operations = [
        // Form filling operations
        healthcarePage.updateAge(25),
        healthcarePage.updateIncome(75000),
        healthcarePage.selectCanton("ZH"),
        healthcarePage.updateHealthStatus("excellent"),
        
        // Navigation operations
        page.click('button:has-text("Optimization")'),
        page.click('button:has-text("FIRE Integration")'),
        page.click('button:has-text("Health Profile")'),
      ];

      const startTime = Date.now();
      await Promise.all(operations);
      const concurrentTime = Date.now() - startTime;
      
      expect(concurrentTime).toBeLessThan(5000); // All operations should complete in under 5 seconds
      console.log(`Concurrent operations completed in ${concurrentTime}ms`);
    });
  });

  test("Large dataset handling", async ({ page }) => {
    await dashboardPage.clickTab("healthcare");

    await test.step("Test with extreme values", async () => {
      const extremeProfile = {
        age: 99,
        canton: "GE",
        income: 1000000,
        healthStatus: "poor" as const,
        familySize: 10,
        hasChildren: true,
        currentPremium: 1000,
        currentDeductible: 300,
        expectedMedicalExpenses: 50000,
        riskTolerance: "low" as const
      };

      const startTime = Date.now();
      await healthcarePage.fillHealthProfile(extremeProfile);
      await healthcarePage.goToOptimization();
      await page.waitForSelector('[data-testid="optimization-results"]');
      const extremeCalculationTime = Date.now() - startTime;
      
      expect(extremeCalculationTime).toBeLessThan(10000); // Even extreme cases should complete in under 10 seconds
      console.log(`Extreme case calculated in ${extremeCalculationTime}ms`);
    });
  });

  test("Network performance simulation", async ({ page, context }) => {
    await test.step("Test with slow network conditions", async () => {
      // Simulate slow 3G network
      await context.route('**/*', async (route) => {
        await new Promise(resolve => setTimeout(resolve, 100)); // Add 100ms delay
        await route.continue();
      });

      const startTime = Date.now();
      await dashboardPage.clickTab("healthcare");
      await page.waitForSelector('h1:has-text("Swiss Healthcare Cost Optimizer")');
      const slowNetworkTime = Date.now() - startTime;
      
      expect(slowNetworkTime).toBeLessThan(10000); // Should still work on slow networks
      console.log(`Healthcare loaded on slow network in ${slowNetworkTime}ms`);
    });
  });

  test("Browser resource monitoring", async ({ page }) => {
    await dashboardPage.clickTab("healthcare");

    await test.step("Monitor CPU and rendering performance", async () => {
      // Start performance monitoring
      await page.evaluate(() => {
        (window as any).performanceMarks = [];
        performance.mark('healthcare-test-start');
      });

      // Perform intensive operations
      const scenario = healthcareTestScenarios.familyBern;
      await healthcarePage.fillHealthProfile(scenario.profile);
      
      for (let i = 0; i < 3; i++) {
        await healthcarePage.goToOptimization();
        await healthcarePage.goToFireIntegration();
        await page.click('button:has-text("Health Profile")');
      }

      // End performance monitoring
      const performanceData = await page.evaluate(() => {
        performance.mark('healthcare-test-end');
        performance.measure('healthcare-test-duration', 'healthcare-test-start', 'healthcare-test-end');
        
        const measure = performance.getEntriesByName('healthcare-test-duration')[0];
        return {
          duration: measure.duration,
          navigationEntries: performance.getEntriesByType('navigation').length,
          resourceEntries: performance.getEntriesByType('resource').length
        };
      });

      expect(performanceData.duration).toBeLessThan(15000); // Total test should complete in under 15 seconds
      console.log(`Performance test completed in ${performanceData.duration.toFixed(2)}ms`);
      console.log(`Navigation entries: ${performanceData.navigationEntries}`);
      console.log(`Resource entries: ${performanceData.resourceEntries}`);
    });
  });

  test("Responsive design performance", async ({ page }) => {
    await test.step("Test performance across different viewport sizes", async () => {
      const viewports = [
        { width: 1920, height: 1080, name: "Desktop Large" },
        { width: 1280, height: 720, name: "Desktop Standard" },
        { width: 768, height: 1024, name: "Tablet" },
        { width: 375, height: 667, name: "Mobile" }
      ];

      for (const viewport of viewports) {
        await page.setViewportSize(viewport);
        
        const startTime = Date.now();
        await dashboardPage.clickTab("healthcare");
        await page.waitForSelector('h1:has-text("Swiss Healthcare Cost Optimizer")');
        const loadTime = Date.now() - startTime;
        
        expect(loadTime).toBeLessThan(5000); // Should load quickly on all devices
        console.log(`${viewport.name} (${viewport.width}x${viewport.height}) loaded in ${loadTime}ms`);
        
        // Test form interaction performance
        const interactionStart = Date.now();
        await healthcarePage.updateAge(35);
        await healthcarePage.selectCanton("ZH");
        const interactionTime = Date.now() - interactionStart;
        
        expect(interactionTime).toBeLessThan(2000); // Interactions should be responsive
        console.log(`${viewport.name} interactions completed in ${interactionTime}ms`);
      }
    });
  });

  test("Stress test with rapid user interactions", async ({ page }) => {
    await dashboardPage.clickTab("healthcare");

    await test.step("Rapid form interactions stress test", async () => {
      const startTime = Date.now();
      
      // Rapid fire interactions
      for (let i = 0; i < 10; i++) {
        await healthcarePage.updateAge(20 + i);
        await healthcarePage.updateIncome(50000 + i * 5000);
        await page.click('button:has-text("Optimization")');
        await page.click('button:has-text("Health Profile")');
      }
      
      const stressTestTime = Date.now() - startTime;
      expect(stressTestTime).toBeLessThan(30000); // Stress test should complete in under 30 seconds
      console.log(`Stress test completed in ${stressTestTime}ms`);
      
      // Verify the application is still responsive
      await healthcarePage.fillHealthProfile(healthcareTestScenarios.youngProfessionalZurich.profile);
      await healthcarePage.expectOptimizationResults();
    });
  });
});
