import { expect, test } from '@playwright/test';
import { DashboardPage } from '../../pages/dashboard-page';

test.describe('Mobile Responsiveness Tests', () => {
  let dashboardPage: DashboardPage;

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
  });

  test.describe('Mobile Layout Adaptation', () => {
    test('adapts layout for mobile portrait (375x667)', async ({ page }) => {
      console.log('📱 Testing mobile portrait layout');
      
      await page.setViewportSize({ width: 375, height: 667 });
      await dashboardPage.goto();
      
      // Verify app loads correctly on mobile
      await page.waitForSelector('h1', { timeout: 10000 });
      const title = await page.locator('h1').first().textContent();
      expect(title).toContain('Swiss Budget Pro');
      
      // Check if navigation is mobile-friendly (hamburger menu or collapsed tabs)
      const mobileNav = page.locator('[data-testid="mobile-navigation"]');
      const hamburgerMenu = page.locator('[data-testid="hamburger-menu"]');
      
      if (await mobileNav.count() > 0) {
        await expect(mobileNav).toBeVisible();
      } else if (await hamburgerMenu.count() > 0) {
        await expect(hamburgerMenu).toBeVisible();
      }
      
      // Verify form inputs are touch-friendly (minimum 44px height)
      const inputs = page.locator('input[type="number"]');
      const inputCount = await inputs.count();
      
      for (let i = 0; i < Math.min(inputCount, 3); i++) {
        const input = inputs.nth(i);
        const boundingBox = await input.boundingBox();
        if (boundingBox) {
          expect(boundingBox.height).toBeGreaterThanOrEqual(44);
        }
      }
    });

    test('adapts layout for mobile landscape (667x375)', async ({ page }) => {
      console.log('📱 Testing mobile landscape layout');
      
      await page.setViewportSize({ width: 667, height: 375 });
      await dashboardPage.goto();
      
      // Verify app loads and is usable in landscape
      await page.waitForSelector('h1', { timeout: 10000 });
      
      // Check that content doesn't overflow horizontally
      const body = page.locator('body');
      const bodyBox = await body.boundingBox();
      expect(bodyBox?.width).toBeLessThanOrEqual(667);
      
      // Verify horizontal scrolling is not needed
      const scrollWidth = await page.evaluate(() => document.body.scrollWidth);
      expect(scrollWidth).toBeLessThanOrEqual(667);
    });

    test('adapts layout for tablet portrait (768x1024)', async ({ page }) => {
      console.log('📱 Testing tablet portrait layout');
      
      await page.setViewportSize({ width: 768, height: 1024 });
      await dashboardPage.goto();
      
      // Verify tablet-optimized layout
      await page.waitForSelector('h1', { timeout: 10000 });
      
      // Tabs should be visible and accessible
      const tabs = page.locator('[role="tab"]');
      const tabCount = await tabs.count();
      
      if (tabCount > 0) {
        for (let i = 0; i < Math.min(tabCount, 4); i++) {
          await expect(tabs.nth(i)).toBeVisible();
        }
      }
      
      // Form should use available space efficiently
      const formContainer = page.locator('[data-testid="form-container"]');
      if (await formContainer.count() > 0) {
        const containerBox = await formContainer.boundingBox();
        expect(containerBox?.width).toBeGreaterThan(400);
      }
    });
  });

  test.describe('Touch Interactions', () => {
    test('supports touch navigation between tabs', async ({ page }) => {
      console.log('👆 Testing touch tab navigation');
      
      await page.setViewportSize({ width: 375, height: 667 });
      await dashboardPage.goto();
      
      // Test touch navigation
      const tabs = page.locator('[role="tab"]');
      const tabCount = await tabs.count();
      
      if (tabCount > 1) {
        // Tap on second tab
        await tabs.nth(1).tap();
        await page.waitForTimeout(500);
        
        // Verify tab switched
        const activeTab = page.locator('[role="tab"][aria-selected="true"]');
        await expect(activeTab).toBeVisible();
      }
    });

    test('supports touch input for form fields', async ({ page }) => {
      console.log('👆 Testing touch form interactions');
      
      await page.setViewportSize({ width: 375, height: 667 });
      await dashboardPage.goto();
      
      // Navigate to income input
      await dashboardPage.navigateToIncomeSubTab();
      
      const incomeInput = page.locator('[data-testid="monthly-income"]');
      
      // Tap to focus
      await incomeInput.tap();
      
      // Verify input is focused
      await expect(incomeInput).toBeFocused();
      
      // Type using touch keyboard simulation
      await incomeInput.fill('8500');
      
      // Verify value was entered
      const value = await incomeInput.inputValue();
      expect(value).toBe('8500');
    });

    test('supports swipe gestures for navigation', async ({ page }) => {
      console.log('👆 Testing swipe navigation');
      
      await page.setViewportSize({ width: 375, height: 667 });
      await dashboardPage.goto();
      
      // Check if swipe navigation is implemented
      const swipeContainer = page.locator('[data-testid="swipe-container"]');
      
      if (await swipeContainer.count() > 0) {
        // Simulate swipe left
        await swipeContainer.hover();
        await page.mouse.down();
        await page.mouse.move(100, 0);
        await page.mouse.up();
        
        await page.waitForTimeout(500);
        
        // Verify navigation occurred
        const activeTab = page.locator('[role="tab"][aria-selected="true"]');
        await expect(activeTab).toBeVisible();
      }
    });
  });

  test.describe('Mobile-Specific Features', () => {
    test('shows mobile-optimized charts and visualizations', async ({ page }) => {
      console.log('📊 Testing mobile chart optimization');
      
      await page.setViewportSize({ width: 375, height: 667 });
      await dashboardPage.goto();
      
      // Input data to generate charts
      await dashboardPage.navigateToIncomeSubTab();
      await dashboardPage.enterMonthlyIncome(8000);
      
      // Navigate to visualizations
      await dashboardPage.navigateToVisualizationsSubTab();
      
      // Check if charts are mobile-optimized
      const charts = page.locator('[data-testid*="chart"]');
      const chartCount = await charts.count();
      
      for (let i = 0; i < Math.min(chartCount, 2); i++) {
        const chart = charts.nth(i);
        const chartBox = await chart.boundingBox();
        
        if (chartBox) {
          // Chart should fit within mobile viewport
          expect(chartBox.width).toBeLessThanOrEqual(375);
          expect(chartBox.height).toBeLessThan(400); // Reasonable height for mobile
        }
      }
    });

    test('provides mobile-friendly data entry', async ({ page }) => {
      console.log('📝 Testing mobile data entry');
      
      await page.setViewportSize({ width: 375, height: 667 });
      await dashboardPage.goto();
      
      await dashboardPage.navigateToIncomeSubTab();
      
      // Check for mobile-specific input enhancements
      const numericInputs = page.locator('input[type="number"]');
      const inputCount = await numericInputs.count();
      
      for (let i = 0; i < Math.min(inputCount, 3); i++) {
        const input = numericInputs.nth(i);
        
        // Should have appropriate input mode for mobile keyboards
        const inputMode = await input.getAttribute('inputmode');
        expect(inputMode === 'numeric' || inputMode === 'decimal').toBeTruthy();
      }
    });

    test('handles mobile keyboard interactions', async ({ page }) => {
      console.log('⌨️ Testing mobile keyboard handling');
      
      await page.setViewportSize({ width: 375, height: 667 });
      await dashboardPage.goto();
      
      await dashboardPage.navigateToIncomeSubTab();
      
      const incomeInput = page.locator('[data-testid="monthly-income"]');
      
      // Focus input to trigger mobile keyboard
      await incomeInput.tap();
      
      // Verify viewport adjusts for keyboard (if implemented)
      await page.waitForTimeout(1000);
      
      // Input should remain visible and accessible
      await expect(incomeInput).toBeVisible();
      
      // Test keyboard navigation
      await page.keyboard.press('Tab');
      
      // Next input should be focused
      const nextInput = page.locator('input[type="number"]').nth(1);
      if (await nextInput.count() > 0) {
        await expect(nextInput).toBeFocused();
      }
    });
  });

  test.describe('Cross-Device Consistency', () => {
    test('maintains data consistency across viewport changes', async ({ page }) => {
      console.log('🔄 Testing data consistency across viewports');
      
      // Start with desktop
      await page.setViewportSize({ width: 1280, height: 720 });
      await dashboardPage.goto();
      
      // Enter data
      await dashboardPage.navigateToIncomeSubTab();
      await dashboardPage.enterMonthlyIncome(9000);
      
      await dashboardPage.navigateToGoalsSubTab();
      await dashboardPage.enterAge(30);
      
      // Switch to mobile
      await page.setViewportSize({ width: 375, height: 667 });
      await page.waitForTimeout(1000);
      
      // Verify data persists
      await dashboardPage.navigateToIncomeSubTab();
      const incomeValue = await page.locator('[data-testid="monthly-income"]').inputValue();
      expect(incomeValue).toBe('9000');
      
      await dashboardPage.navigateToGoalsSubTab();
      const ageValue = await page.locator('[data-testid="age-input"]').inputValue();
      expect(ageValue).toBe('30');
    });

    test('provides consistent functionality across devices', async ({ page }) => {
      console.log('⚙️ Testing functionality consistency');
      
      const viewports = [
        { width: 375, height: 667, name: 'Mobile' },
        { width: 768, height: 1024, name: 'Tablet' },
        { width: 1280, height: 720, name: 'Desktop' }
      ];
      
      for (const viewport of viewports) {
        console.log(`Testing ${viewport.name} viewport`);
        
        await page.setViewportSize({ width: viewport.width, height: viewport.height });
        await dashboardPage.goto();
        
        // Test core functionality
        await dashboardPage.navigateToIncomeSubTab();
        await dashboardPage.enterMonthlyIncome(8000);
        
        // Verify calculations work
        await dashboardPage.waitForCalculations();
        
        // Navigate to dashboard to check results
        await dashboardPage.navigateToDashboardTab();
        
        const fireProjection = page.locator('[data-testid="fire-projection"]');
        await expect(fireProjection).toBeVisible();
        
        console.log(`✅ ${viewport.name} viewport functionality verified`);
      }
    });
  });

  test.describe('Performance on Mobile', () => {
    test('loads quickly on mobile devices', async ({ page }) => {
      console.log('⚡ Testing mobile load performance');
      
      await page.setViewportSize({ width: 375, height: 667 });
      
      const startTime = Date.now();
      await dashboardPage.goto();
      await page.waitForSelector('h1', { timeout: 10000 });
      const loadTime = Date.now() - startTime;
      
      // Mobile should load within reasonable time (allowing for slower mobile networks)
      expect(loadTime).toBeLessThan(5000);
      
      console.log(`📱 Mobile load time: ${loadTime}ms`);
    });

    test('handles calculations efficiently on mobile', async ({ page }) => {
      console.log('⚡ Testing mobile calculation performance');
      
      await page.setViewportSize({ width: 375, height: 667 });
      await dashboardPage.goto();
      
      await dashboardPage.navigateToIncomeSubTab();
      
      const startTime = Date.now();
      await dashboardPage.enterMonthlyIncome(10000);
      await dashboardPage.waitForCalculations();
      const calculationTime = Date.now() - startTime;
      
      // Calculations should be responsive on mobile
      expect(calculationTime).toBeLessThan(2000);
      
      console.log(`📱 Mobile calculation time: ${calculationTime}ms`);
    });
  });
});
