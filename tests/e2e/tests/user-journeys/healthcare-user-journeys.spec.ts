import { expect, test } from "@playwright/test";
import { DashboardPage } from "../../pages/dashboard-page";
import { HealthcareOptimizerPage } from "../../pages/healthcare-optimizer-page";
import { healthcareTestScenarios } from "../../fixtures/healthcare-scenarios";

test.describe("Healthcare Cost Optimizer - Complete User Journeys", () => {
  let dashboardPage: DashboardPage;
  let healthcarePage: HealthcareOptimizerPage;

  test.describe.configure({ mode: "serial", timeout: 120000 });

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
    healthcarePage = new HealthcareOptimizerPage(page);
    await dashboardPage.goto();
    await dashboardPage.clearLocalStorage();
  });

  test("Complete optimization journey for young professional", async ({ page }) => {
    const scenario = healthcareTestScenarios.youngProfessionalZurich;

    await test.step("User discovers healthcare optimization feature", async () => {
      // User starts on main dashboard
      await expect(page.locator('h1')).toBeVisible();
      
      // User notices healthcare tab
      await expect(page.locator('button:has-text("Healthcare")')).toBeVisible();
      
      // User clicks to explore healthcare optimization
      await dashboardPage.clickTab("healthcare");
      await expect(page.locator('h1:has-text("Swiss Healthcare Cost Optimizer")')).toBeVisible();
    });

    await test.step("User fills health profile with personal information", async () => {
      // User reads the introduction and starts filling the form
      await expect(page.locator('text=Health Profile')).toBeVisible();
      
      // User enters basic information
      await healthcarePage.updateAge(scenario.profile.age);
      await healthcarePage.selectCanton(scenario.profile.canton);
      await healthcarePage.updateIncome(scenario.profile.income);
      
      // User selects health status
      await healthcarePage.updateHealthStatus(scenario.profile.healthStatus);
      
      // User enters family information
      await healthcarePage.updateFamilySize(scenario.profile.familySize);
      await healthcarePage.updateHasChildren(scenario.profile.hasChildren);
      
      // User enters current insurance information
      await healthcarePage.updateCurrentPremium(scenario.profile.currentPremium);
      await healthcarePage.updateCurrentDeductible(scenario.profile.currentDeductible);
      
      // User estimates medical expenses
      await healthcarePage.updateExpectedExpenses(scenario.profile.expectedMedicalExpenses);
      
      // User sets risk tolerance
      await healthcarePage.updateRiskTolerance(scenario.profile.riskTolerance);
    });

    await test.step("User reviews optimization recommendations", async () => {
      // User navigates to see optimization results
      await page.click('button:has-text("Optimization")');
      await page.waitForSelector('[data-testid="optimization-results"]');
      
      // User sees potential savings
      const totalSavings = await healthcarePage.getTotalSavings();
      expect(totalSavings).toBeGreaterThan(scenario.expectedResults.minSavings);
      expect(totalSavings).toBeLessThan(scenario.expectedResults.maxSavings);
      
      // User reviews deductible recommendation
      const recommendedDeductible = await healthcarePage.getRecommendedDeductible();
      expect(recommendedDeductible).toBe(scenario.expectedResults.optimalDeductible);
      
      // User examines insurance recommendations
      const recommendationCount = await healthcarePage.getInsuranceRecommendationCount();
      expect(recommendationCount).toBe(scenario.testCriteria.expectedRecommendationCount);
      
      // User reads pros and cons of each recommendation
      const firstRecommendation = page.locator('[data-testid="insurance-recommendation"]').first();
      await expect(firstRecommendation.locator('text=Pros:')).toBeVisible();
      await expect(firstRecommendation.locator('text=Quality:')).toBeVisible();
    });

    await test.step("User explores FIRE integration", async () => {
      // User wants to understand impact on FIRE goals
      await page.click('button:has-text("FIRE Integration")');
      await page.waitForSelector('[data-testid="fire-analysis"]');
      
      // User reviews FIRE impact
      const additionalFireNumber = await healthcarePage.getAdditionalFireNumber();
      expect(additionalFireNumber).toBeGreaterThan(0);
      
      const fireDelayMonths = await healthcarePage.getFireDelayMonths();
      expect(fireDelayMonths).toBeLessThan(scenario.expectedResults.fireImpact.delayMonths * 1.5);
      
      const healthcarePercentage = await healthcarePage.getHealthcarePercentage();
      expect(healthcarePercentage).toBeGreaterThan(0);
      
      // User reads optimization strategies
      await expect(page.locator('text=FIRE Optimization Strategies')).toBeVisible();
      const strategies = page.locator('[data-testid="strategy-recommendation"]');
      expect(await strategies.count()).toBeGreaterThan(0);
    });

    await test.step("User creates action plan", async () => {
      // User moves to action planning
      await page.click('button:has-text("Action Plan")');
      
      // User reviews actionable recommendations
      await expect(page.locator('text=Action Plan')).toBeVisible();
      
      // User can see next steps (when implemented)
      // This would include specific actions like "Switch to Assura with CHF 2500 deductible"
    });

    await test.step("User compares with current situation", async () => {
      // User goes back to review their current setup
      await page.click('button:has-text("Health Profile")');
      
      // User verifies their current information is still there
      const currentValues = await healthcarePage.getCurrentFormValues();
      expect(currentValues.age).toBe(scenario.profile.age);
      expect(currentValues.canton).toBe(scenario.profile.canton);
      expect(currentValues.currentPremium).toBe(scenario.profile.currentPremium);
      
      // User can easily compare current vs optimized
      await page.click('button:has-text("Optimization")');
      const savings = await healthcarePage.getTotalSavings();
      expect(savings).toBeGreaterThan(0);
    });
  });

  test("Family healthcare optimization journey", async ({ page }) => {
    const scenario = healthcareTestScenarios.familyBern;

    await test.step("Family user explores healthcare costs", async () => {
      await dashboardPage.clickTab("healthcare");
      
      // Family user has different considerations
      await healthcarePage.fillHealthProfile(scenario.profile);
      
      // Family user is particularly interested in family-specific recommendations
      await page.click('button:has-text("Optimization")');
      await page.waitForSelector('[data-testid="optimization-results"]');
      
      // Family should see family-oriented recommendations
      const recommendations = page.locator('[data-testid="insurance-recommendation"]');
      expect(await recommendations.count()).toBeGreaterThan(0);
    });

    await test.step("Family user evaluates risk vs savings", async () => {
      // Family user is more risk-averse
      const recommendedDeductible = await healthcarePage.getRecommendedDeductible();
      expect(recommendedDeductible).toBeLessThanOrEqual(2000); // Families typically prefer lower deductibles
      
      // Family user checks confidence level
      const confidenceLevel = await healthcarePage.getConfidenceLevel();
      expect(confidenceLevel).toBeGreaterThan(50);
    });

    await test.step("Family user considers FIRE impact", async () => {
      await page.click('button:has-text("FIRE Integration")');
      await page.waitForSelector('[data-testid="fire-analysis"]');
      
      // Family healthcare costs should be higher
      const averageAnnualCost = await healthcarePage.getAverageAnnualCost();
      expect(averageAnnualCost).toBeGreaterThan(5000); // Family costs are higher
      
      // Family user sees longer FIRE timeline impact
      const fireDelayMonths = await healthcarePage.getFireDelayMonths();
      expect(fireDelayMonths).toBeGreaterThan(12); // Families typically have longer delays
    });
  });

  test("Low-income subsidy optimization journey", async ({ page }) => {
    const scenario = healthcareTestScenarios.lowIncomeVaud;

    await test.step("Low-income user discovers subsidy opportunities", async () => {
      await dashboardPage.clickTab("healthcare");
      await healthcarePage.fillHealthProfile(scenario.profile);
      
      await page.click('button:has-text("FIRE Integration")');
      await page.waitForSelector('[data-testid="fire-analysis"]');
      
      // Low-income user should see significant subsidy optimization potential
      const subsidyOptimization = page.locator('[data-testid="subsidy-optimization"]');
      await expect(subsidyOptimization).toBeVisible();
      
      const subsidyAmount = await subsidyOptimization.textContent();
      const subsidyValue = parseInt(subsidyAmount?.replace(/[^\d]/g, '') || '0');
      expect(subsidyValue).toBeGreaterThan(1000); // Should show significant subsidy potential
    });

    await test.step("Low-income user explores geographic arbitrage", async () => {
      // User tests different cantons to see subsidy differences
      await page.click('button:has-text("Health Profile")');
      
      const cantons = ["VD", "JU", "VS", "TI"];
      const cantonSavings: Record<string, number> = {};
      
      for (const canton of cantons) {
        await healthcarePage.selectCanton(canton);
        await page.click('button:has-text("Optimization")');
        await page.waitForSelector('[data-testid="optimization-results"]');
        
        cantonSavings[canton] = await healthcarePage.getTotalSavings();
        
        await page.click('button:has-text("Health Profile")');
      }
      
      // Some cantons should offer better savings for low-income users
      const maxSavings = Math.max(...Object.values(cantonSavings));
      expect(maxSavings).toBeGreaterThan(scenario.expectedResults.minSavings);
    });
  });

  test("Senior pre-retirement healthcare planning journey", async ({ page }) => {
    const scenario = healthcareTestScenarios.seniorBasel;

    await test.step("Senior user focuses on healthcare security", async () => {
      await dashboardPage.clickTab("healthcare");
      await healthcarePage.fillHealthProfile(scenario.profile);
      
      await page.click('button:has-text("Optimization")');
      await page.waitForSelector('[data-testid="optimization-results"]');
      
      // Senior user should see conservative recommendations
      const recommendedDeductible = await healthcarePage.getRecommendedDeductible();
      expect(recommendedDeductible).toBeLessThanOrEqual(1000); // Seniors prefer lower deductibles
    });

    await test.step("Senior user evaluates pre-retirement healthcare costs", async () => {
      await page.click('button:has-text("FIRE Integration")');
      await page.waitForSelector('[data-testid="fire-analysis"]');
      
      // Senior user sees higher healthcare costs
      const averageAnnualCost = await healthcarePage.getAverageAnnualCost();
      expect(averageAnnualCost).toBeGreaterThan(8000); // Senior costs are higher
      
      // Senior user sees significant FIRE impact
      const healthcarePercentage = await healthcarePage.getHealthcarePercentage();
      expect(healthcarePercentage).toBeGreaterThan(20); // Healthcare is major expense for seniors
    });

    await test.step("Senior user plans for AHV transition", async () => {
      // Senior user should see information about AHV transition
      await expect(page.locator('text=AHV')).toBeVisible();
      
      // Senior user sees years until AHV eligibility
      const fireAnalysis = page.locator('[data-testid="fire-analysis"]');
      await expect(fireAnalysis).toContainText('65'); // AHV age
    });
  });

  test("FIRE candidate optimization journey", async ({ page }) => {
    const scenario = healthcareTestScenarios.fireCandidate;

    await test.step("FIRE candidate optimizes for early retirement", async () => {
      await dashboardPage.clickTab("healthcare");
      await healthcarePage.fillHealthProfile(scenario.profile);
      
      await page.click('button:has-text("Optimization")');
      await page.waitForSelector('[data-testid="optimization-results"]');
      
      // FIRE candidate should see aggressive optimization
      const totalSavings = await healthcarePage.getTotalSavings();
      expect(totalSavings).toBeGreaterThan(scenario.expectedResults.minSavings);
      
      // FIRE candidate accepts higher deductibles
      const recommendedDeductible = await healthcarePage.getRecommendedDeductible();
      expect(recommendedDeductible).toBeGreaterThanOrEqual(2000);
    });

    await test.step("FIRE candidate evaluates healthcare FIRE number", async () => {
      await page.click('button:has-text("FIRE Integration")');
      await page.waitForSelector('[data-testid="fire-analysis"]');
      
      // FIRE candidate sees additional capital needed
      const additionalFireNumber = await healthcarePage.getAdditionalFireNumber();
      expect(additionalFireNumber).toBeGreaterThan(20000);
      expect(additionalFireNumber).toBeLessThan(100000); // Should be reasonable for healthy person
      
      // FIRE candidate sees minimal delay
      const fireDelayMonths = await healthcarePage.getFireDelayMonths();
      expect(fireDelayMonths).toBeLessThan(12); // Optimized healthcare shouldn't delay FIRE much
    });

    await test.step("FIRE candidate explores geographic arbitrage", async () => {
      // FIRE candidate considers moving to optimize costs
      await page.click('button:has-text("Health Profile")');
      
      // Test low-cost cantons
      const lowCostCantons = ["ZG", "SZ", "NW"];
      let bestSavings = 0;
      
      for (const canton of lowCostCantons) {
        await healthcarePage.selectCanton(canton);
        await page.click('button:has-text("Optimization")');
        await page.waitForSelector('[data-testid="optimization-results"]');
        
        const savings = await healthcarePage.getTotalSavings();
        bestSavings = Math.max(bestSavings, savings);
        
        await page.click('button:has-text("Health Profile")');
      }
      
      expect(bestSavings).toBeGreaterThan(500); // Should find meaningful savings
    });
  });

  test("Cross-platform healthcare optimization journey", async ({ page }) => {
    const scenario = healthcareTestScenarios.youngProfessionalZurich;

    await test.step("User starts on desktop", async () => {
      // Desktop experience
      await page.setViewportSize({ width: 1280, height: 720 });
      await dashboardPage.clickTab("healthcare");
      await healthcarePage.fillHealthProfile(scenario.profile);
      
      // User saves progress (implicit through form state)
      await page.click('button:has-text("Optimization")');
      const desktopSavings = await healthcarePage.getTotalSavings();
      expect(desktopSavings).toBeGreaterThan(0);
    });

    await test.step("User continues on mobile", async () => {
      // Switch to mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      // User should see same data on mobile
      await page.click('button:has-text("Health Profile")');
      const currentValues = await healthcarePage.getCurrentFormValues();
      expect(currentValues.age).toBe(scenario.profile.age);
      
      // Mobile optimization should work
      await page.click('button:has-text("Optimization")');
      const mobileSavings = await healthcarePage.getTotalSavings();
      expect(mobileSavings).toBe(desktopSavings); // Should be consistent
    });

    await test.step("User completes journey on tablet", async () => {
      // Switch to tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });
      
      // User can complete FIRE analysis on tablet
      await page.click('button:has-text("FIRE Integration")');
      await page.waitForSelector('[data-testid="fire-analysis"]');
      
      const fireImpact = await healthcarePage.getAdditionalFireNumber();
      expect(fireImpact).toBeGreaterThan(0);
    });
  });
});
