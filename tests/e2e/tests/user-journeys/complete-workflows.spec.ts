import { expect, test } from '@playwright/test';
import { DashboardPage } from '../../pages/dashboard-page';
import { swissTestScenarios } from '../../fixtures/swiss-scenarios';

test.describe('Complete User Journey Workflows', () => {
  let dashboardPage: DashboardPage;

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
    await dashboardPage.goto();
    await dashboardPage.clearLocalStorage();
  });

  test.describe('First-Time User Onboarding', () => {
    test('guides new user through complete setup', async ({ page }) => {
      console.log('👋 Testing first-time user onboarding');
      
      // Check for onboarding wizard or welcome screen
      const onboardingWizard = page.locator('[data-testid="onboarding-wizard"]');
      const welcomeScreen = page.locator('[data-testid="welcome-screen"]');
      
      if (await onboardingWizard.count() > 0) {
        console.log('📋 Onboarding wizard detected');
        
        // Step 1: Personal Information
        await expect(onboardingWizard).toBeVisible();
        
        const nextButton = page.locator('[data-testid="onboarding-next"]');
        const ageInput = page.locator('[data-testid="onboarding-age"]');
        
        if (await ageInput.count() > 0) {
          await ageInput.fill('30');
          await nextButton.click();
        }
        
        // Step 2: Location Information
        const cantonSelect = page.locator('[data-testid="onboarding-canton"]');
        if (await cantonSelect.count() > 0) {
          await cantonSelect.selectOption('ZH');
          await nextButton.click();
        }
        
        // Step 3: Income Information
        const incomeInput = page.locator('[data-testid="onboarding-income"]');
        if (await incomeInput.count() > 0) {
          await incomeInput.fill('8500');
          await nextButton.click();
        }
        
        // Complete onboarding
        const completeButton = page.locator('[data-testid="onboarding-complete"]');
        if (await completeButton.count() > 0) {
          await completeButton.click();
        }
        
        // Should navigate to main dashboard
        await page.waitForTimeout(1000);
        await expect(page.locator('h1')).toContainText('Swiss Budget Pro');
        
      } else if (await welcomeScreen.count() > 0) {
        console.log('👋 Welcome screen detected');
        
        const getStartedButton = page.locator('[data-testid="get-started"]');
        if (await getStartedButton.count() > 0) {
          await getStartedButton.click();
        }
      }
      
      // Verify user can start using the app
      await dashboardPage.navigateToIncomeSubTab();
      const incomeInput = page.locator('[data-testid="monthly-income"]');
      await expect(incomeInput).toBeVisible();
      
      console.log('✅ First-time user onboarding completed');
    });

    test('provides helpful tooltips and guidance', async ({ page }) => {
      console.log('💡 Testing tooltips and guidance');
      
      await dashboardPage.navigateToIncomeSubTab();
      
      // Check for help tooltips
      const helpIcons = page.locator('[data-testid*="help-icon"]');
      const tooltips = page.locator('[data-testid*="tooltip"]');
      
      const helpIconCount = await helpIcons.count();
      
      if (helpIconCount > 0) {
        // Test tooltip interactions
        for (let i = 0; i < Math.min(helpIconCount, 3); i++) {
          const helpIcon = helpIcons.nth(i);
          await helpIcon.hover();
          
          // Tooltip should appear
          await page.waitForTimeout(500);
          const visibleTooltips = await tooltips.count();
          expect(visibleTooltips).toBeGreaterThan(0);
          
          // Move away to hide tooltip
          await page.locator('body').hover();
          await page.waitForTimeout(300);
        }
      }
      
      // Check for contextual help
      const helpButton = page.locator('[data-testid="help-button"]');
      if (await helpButton.count() > 0) {
        await helpButton.click();
        
        const helpPanel = page.locator('[data-testid="help-panel"]');
        await expect(helpPanel).toBeVisible();
        
        // Close help panel
        const closeHelp = page.locator('[data-testid="close-help"]');
        if (await closeHelp.count() > 0) {
          await closeHelp.click();
        }
      }
      
      console.log('✅ Tooltips and guidance verified');
    });
  });

  test.describe('Complete Financial Planning Journey', () => {
    test('young professional planning for early retirement', async ({ page }) => {
      console.log('🎯 Testing young professional FIRE journey');
      
      // Scenario: 28-year-old in Zurich planning for FIRE
      const scenario = swissTestScenarios.zurichProfessional;
      
      // Step 1: Input basic information
      await test.step('Input personal and income information', async () => {
        await dashboardPage.navigateToIncomeSubTab();
        await dashboardPage.enterMonthlyIncome(scenario.income.monthly);
        
        await dashboardPage.navigateToGoalsSubTab();
        await dashboardPage.enterAge(scenario.personalInfo.age);
        
        console.log('✅ Basic information entered');
      });
      
      // Step 2: Configure tax optimization
      await test.step('Configure tax settings', async () => {
        await dashboardPage.navigateToTaxOptimizationSubTab();
        await dashboardPage.selectCanton(scenario.personalInfo.canton);
        await dashboardPage.selectCivilStatus(scenario.personalInfo.civilStatus);
        
        console.log('✅ Tax settings configured');
      });
      
      // Step 3: Review projections
      await test.step('Review FIRE projections', async () => {
        await dashboardPage.navigateToDashboardTab();
        await dashboardPage.waitForCalculations();
        
        // Verify FIRE projection is reasonable
        const fireProjection = page.locator('[data-testid="fire-projection"]');
        await expect(fireProjection).toBeVisible();
        
        const projectionText = await fireProjection.textContent();
        expect(projectionText).toMatch(/age|years/i);
        
        console.log('✅ FIRE projections reviewed');
      });
      
      // Step 4: Explore optimization strategies
      await test.step('Explore optimization strategies', async () => {
        await dashboardPage.navigateToTaxOptimizationSubTab();
        
        // Check Pillar 3a recommendations
        const pillar3aRecommendation = page.locator('[data-testid="pillar3a-recommendation"]');
        if (await pillar3aRecommendation.count() > 0) {
          await expect(pillar3aRecommendation).toBeVisible();
        }
        
        // Check tax optimization suggestions
        const taxOptimizations = page.locator('[data-testid="tax-optimization-suggestions"]');
        if (await taxOptimizations.count() > 0) {
          await expect(taxOptimizations).toBeVisible();
        }
        
        console.log('✅ Optimization strategies explored');
      });
      
      // Step 5: Save and export plan
      await test.step('Save and export financial plan', async () => {
        await dashboardPage.navigateToDataSubTab();
        
        // Save plan
        const saveButton = page.locator('[data-testid="save-plan"]');
        if (await saveButton.count() > 0) {
          await saveButton.click();
          await page.waitForTimeout(1000);
        }
        
        // Export data
        const exportButton = page.locator('[data-testid="export-data"]');
        if (await exportButton.count() > 0) {
          const downloadPromise = page.waitForEvent('download');
          await exportButton.click();
          const download = await downloadPromise;
          expect(download.suggestedFilename()).toMatch(/\.json$/);
        }
        
        console.log('✅ Plan saved and exported');
      });
      
      console.log('🎉 Young professional FIRE journey completed');
    });

    test('family financial planning with children', async ({ page }) => {
      console.log('👨‍👩‍👧‍👦 Testing family financial planning journey');
      
      const scenario = swissTestScenarios.vaudFamily;
      
      // Step 1: Input family information
      await test.step('Input family financial information', async () => {
        await dashboardPage.inputScenario(scenario);
        console.log('✅ Family information entered');
      });
      
      // Step 2: Review family-specific calculations
      await test.step('Review family tax implications', async () => {
        await dashboardPage.navigateToTaxOptimizationSubTab();
        await dashboardPage.waitForCalculations();
        
        // Family should have different tax treatment
        const monthlyTax = await dashboardPage.getMonthlyTaxAmount();
        expect(monthlyTax).toBeGreaterThan(0);
        
        // Check for family-specific deductions
        const familyDeductions = page.locator('[data-testid="family-deductions"]');
        if (await familyDeductions.count() > 0) {
          await expect(familyDeductions).toBeVisible();
        }
        
        console.log('✅ Family tax implications reviewed');
      });
      
      // Step 3: Plan for children's education
      await test.step('Plan for education expenses', async () => {
        await dashboardPage.navigateToGoalsSubTab();
        
        // Check for education planning features
        const educationPlanning = page.locator('[data-testid="education-planning"]');
        if (await educationPlanning.count() > 0) {
          await expect(educationPlanning).toBeVisible();
          
          // Input education costs
          const educationCosts = page.locator('[data-testid="education-costs"]');
          if (await educationCosts.count() > 0) {
            await educationCosts.fill('50000');
          }
        }
        
        console.log('✅ Education planning configured');
      });
      
      // Step 4: Review long-term projections
      await test.step('Review long-term family projections', async () => {
        await dashboardPage.navigateToVisualizationsSubTab();
        
        // Should show family-specific projections
        const familyProjections = page.locator('[data-testid="family-projections"]');
        if (await familyProjections.count() > 0) {
          await expect(familyProjections).toBeVisible();
        }
        
        console.log('✅ Long-term projections reviewed');
      });
      
      console.log('🎉 Family financial planning journey completed');
    });

    test('high-income executive wealth optimization', async ({ page }) => {
      console.log('💼 Testing executive wealth optimization journey');
      
      const scenario = swissTestScenarios.genevaExecutive;
      
      // Step 1: Input high-income scenario
      await test.step('Input executive compensation', async () => {
        await dashboardPage.inputScenario(scenario);
        console.log('✅ Executive compensation entered');
      });
      
      // Step 2: Analyze wealth tax implications
      await test.step('Analyze wealth tax implications', async () => {
        await dashboardPage.navigateToTaxOptimizationSubTab();
        
        // High income should trigger wealth tax analysis
        const wealthTaxAnalysis = page.locator('[data-testid="wealth-tax-analysis"]');
        if (await wealthTaxAnalysis.count() > 0) {
          await expect(wealthTaxAnalysis).toBeVisible();
        }
        
        // Check for high-income optimization strategies
        const highIncomeStrategies = page.locator('[data-testid="high-income-strategies"]');
        if (await highIncomeStrategies.count() > 0) {
          await expect(highIncomeStrategies).toBeVisible();
        }
        
        console.log('✅ Wealth tax implications analyzed');
      });
      
      // Step 3: Explore investment strategies
      await test.step('Explore investment strategies', async () => {
        await dashboardPage.navigateToSavingsSubTab();
        
        // Should show advanced investment options
        const investmentStrategies = page.locator('[data-testid="investment-strategies"]');
        if (await investmentStrategies.count() > 0) {
          await expect(investmentStrategies).toBeVisible();
        }
        
        console.log('✅ Investment strategies explored');
      });
      
      // Step 4: Generate comprehensive report
      await test.step('Generate executive financial report', async () => {
        await dashboardPage.navigateToReportsSubTab();
        
        // Generate detailed report
        const generateReport = page.locator('[data-testid="generate-detailed-report"]');
        if (await generateReport.count() > 0) {
          await generateReport.click();
          await page.waitForTimeout(2000);
          
          // Report should be generated
          const reportContent = page.locator('[data-testid="report-content"]');
          if (await reportContent.count() > 0) {
            await expect(reportContent).toBeVisible();
          }
        }
        
        console.log('✅ Executive report generated');
      });
      
      console.log('🎉 Executive wealth optimization journey completed');
    });
  });

  test.describe('Scenario Comparison Workflows', () => {
    test('compares different canton tax scenarios', async ({ page }) => {
      console.log('🏔️ Testing canton comparison workflow');
      
      const baseScenario = swissTestScenarios.zurichProfessional;
      await dashboardPage.inputScenario(baseScenario);
      
      // Test different cantons
      const cantons = ['ZH', 'ZG', 'GE', 'VD'];
      const taxResults: { canton: string; tax: number }[] = [];
      
      for (const canton of cantons) {
        await dashboardPage.navigateToTaxOptimizationSubTab();
        await dashboardPage.selectCanton(canton);
        await dashboardPage.waitForCalculations();
        
        const monthlyTax = await dashboardPage.getMonthlyTaxAmount();
        taxResults.push({ canton, tax: monthlyTax });
        
        console.log(`Canton ${canton}: CHF ${monthlyTax} monthly tax`);
      }
      
      // Verify tax differences between cantons
      const taxAmounts = taxResults.map(r => r.tax);
      const minTax = Math.min(...taxAmounts);
      const maxTax = Math.max(...taxAmounts);
      
      expect(maxTax).toBeGreaterThan(minTax); // Should have differences
      
      console.log('✅ Canton comparison completed');
    });

    test('compares civil status impact on finances', async ({ page }) => {
      console.log('💑 Testing civil status comparison workflow');
      
      const baseScenario = swissTestScenarios.genevaExecutive;
      await dashboardPage.inputScenario(baseScenario);
      
      // Test different civil statuses
      const statuses = ['single', 'married'];
      const statusResults: { status: string; tax: number; savings: number }[] = [];
      
      for (const status of statuses) {
        await dashboardPage.navigateToTaxOptimizationSubTab();
        await dashboardPage.selectCivilStatus(status);
        await dashboardPage.waitForCalculations();
        
        const monthlyTax = await dashboardPage.getMonthlyTaxAmount();
        const savingsRate = await dashboardPage.getSavingsRate();
        
        statusResults.push({ status, tax: monthlyTax, savings: savingsRate });
        
        console.log(`Status ${status}: CHF ${monthlyTax} tax, ${savingsRate}% savings`);
      }
      
      // Verify impact of civil status
      expect(statusResults.length).toBe(2);
      expect(statusResults[0].tax).not.toBe(statusResults[1].tax);
      
      console.log('✅ Civil status comparison completed');
    });
  });

  test.describe('Error Recovery Workflows', () => {
    test('recovers from invalid data entry', async ({ page }) => {
      console.log('🔧 Testing error recovery workflow');
      
      // Enter invalid data
      await dashboardPage.navigateToIncomeSubTab();
      await dashboardPage.enterMonthlyIncome(-5000);
      
      await dashboardPage.navigateToGoalsSubTab();
      await dashboardPage.enterAge(200);
      
      // Should show validation errors
      const incomeError = page.locator('[data-testid="monthly-income-error"]');
      const ageError = page.locator('[data-testid="age-error"]');
      
      if (await incomeError.count() > 0) {
        await expect(incomeError).toBeVisible();
      }
      
      if (await ageError.count() > 0) {
        await expect(ageError).toBeVisible();
      }
      
      // Correct the errors
      await dashboardPage.navigateToIncomeSubTab();
      await dashboardPage.enterMonthlyIncome(8500);
      
      await dashboardPage.navigateToGoalsSubTab();
      await dashboardPage.enterAge(30);
      
      // Errors should be cleared
      await page.waitForTimeout(1000);
      
      if (await incomeError.count() > 0) {
        await expect(incomeError).toBeHidden();
      }
      
      if (await ageError.count() > 0) {
        await expect(ageError).toBeHidden();
      }
      
      // Should be able to proceed normally
      await dashboardPage.navigateToDashboardTab();
      const fireProjection = page.locator('[data-testid="fire-projection"]');
      await expect(fireProjection).toBeVisible();
      
      console.log('✅ Error recovery workflow completed');
    });
  });
});
