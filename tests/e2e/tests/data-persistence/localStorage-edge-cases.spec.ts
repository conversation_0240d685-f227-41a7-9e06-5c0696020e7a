import { expect, test } from '@playwright/test';
import { DashboardPage } from '../../pages/dashboard-page';
import { swissTestScenarios } from '../../fixtures/swiss-scenarios';

test.describe('Data Persistence and Edge Cases', () => {
  let dashboardPage: DashboardPage;

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
    await dashboardPage.goto();
    await dashboardPage.waitForPageLoad();
  });

  test('localStorage data persistence across sessions', async ({ page }) => {
    console.log('💾 Testing localStorage persistence');
    
    const scenario = swissTestScenarios.zurichProfessional;
    await dashboardPage.inputScenario(scenario);
    
    // Verify data is saved
    const savedIncome = await dashboardPage.monthlyIncomeInput.inputValue();
    expect(savedIncome).toBe(scenario.income.monthly.toString());
    
    // Reload page and verify data persists
    await page.reload();
    await dashboardPage.waitForPageLoad();
    
    const persistedIncome = await dashboardPage.monthlyIncomeInput.inputValue();
    expect(persistedIncome).toBe(scenario.income.monthly.toString());
    
    console.log('✅ Data persistence verified');
  });

  test('localStorage quota exceeded handling', async ({ page }) => {
    console.log('📦 Testing localStorage quota handling');
    
    // Fill localStorage with large amounts of data
    await page.evaluate(() => {
      try {
        for (let i = 0; i < 1000; i++) {
          localStorage.setItem(`test_key_${i}`, 'x'.repeat(1000));
        }
      } catch (e) {
        console.log('localStorage quota exceeded as expected');
      }
    });
    
    // App should still function
    const scenario = swissTestScenarios.bernConservative;
    await dashboardPage.inputScenario(scenario);
    
    // Verify calculations still work
    await expect(dashboardPage.fireProjection).toBeVisible();
    await expect(dashboardPage.monthlyTaxAmount).toBeVisible();
    
    // Clean up
    await page.evaluate(() => {
      for (let i = 0; i < 1000; i++) {
        localStorage.removeItem(`test_key_${i}`);
      }
    });
    
    console.log('✅ localStorage quota handling verified');
  });

  test('Corrupted localStorage data handling', async ({ page }) => {
    console.log('🔧 Testing corrupted data handling');
    
    // Inject corrupted data into localStorage
    await page.evaluate(() => {
      localStorage.setItem('swissBudgetPro_data', '{"invalid": json}');
      localStorage.setItem('swissBudgetPro_preferences', 'not json at all');
    });
    
    // Reload page
    await page.reload();
    await dashboardPage.waitForPageLoad();
    
    // App should handle corrupted data gracefully
    await expect(dashboardPage.monthlyIncomeInput).toBeVisible();
    
    // Should be able to input new data
    const scenario = swissTestScenarios.vaudFamily;
    await dashboardPage.inputScenario(scenario);
    
    await expect(dashboardPage.fireProjection).toBeVisible();
    
    console.log('✅ Corrupted data handling verified');
  });

  test('Multiple browser tabs synchronization', async ({ browser }) => {
    console.log('🔄 Testing multi-tab synchronization');
    
    // Create two tabs
    const context = await browser.newContext();
    const page1 = await context.newPage();
    const page2 = await context.newPage();
    
    const dashboard1 = new DashboardPage(page1);
    const dashboard2 = new DashboardPage(page2);
    
    await dashboard1.goto();
    await dashboard1.waitForPageLoad();
    
    await dashboard2.goto();
    await dashboard2.waitForPageLoad();
    
    // Input data in first tab
    const scenario = swissTestScenarios.genevaExecutive;
    await dashboard1.inputScenario(scenario);
    
    // Reload second tab to get updated data
    await page2.reload();
    await dashboard2.waitForPageLoad();
    
    // Data should be synchronized
    const income1 = await dashboard1.monthlyIncomeInput.inputValue();
    const income2 = await dashboard2.monthlyIncomeInput.inputValue();
    
    expect(income1).toBe(income2);
    
    await context.close();
    console.log('✅ Multi-tab synchronization verified');
  });

  test('Edge case: Extreme input values', async ({ page }) => {
    console.log('🔢 Testing extreme input values');
    
    // Test very large income
    await dashboardPage.enterMonthlyIncome(999999);
    await dashboardPage.waitForCalculations();
    
    // Should handle large numbers gracefully
    const fireProjection = await dashboardPage.fireProjection.textContent();
    expect(fireProjection).toContain('CHF');
    
    // Test very small income
    await dashboardPage.enterMonthlyIncome(1);
    await dashboardPage.waitForCalculations();
    
    const smallIncomeProjection = await dashboardPage.fireProjection.textContent();
    expect(smallIncomeProjection).toContain('CHF');
    
    // Test extreme age values
    await dashboardPage.enterAge(18); // Minimum working age
    await dashboardPage.waitForCalculations();
    
    await dashboardPage.enterAge(99); // Very old
    await dashboardPage.waitForCalculations();
    
    // Should still show calculations
    await expect(dashboardPage.fireProjection).toBeVisible();
    
    console.log('✅ Extreme input values handled');
  });

  test('Edge case: Invalid canton codes', async ({ page }) => {
    console.log('🗺️ Testing invalid canton handling');
    
    // Inject invalid canton code via localStorage
    await page.evaluate(() => {
      const data = {
        selectedCanton: 'INVALID',
        monthlyIncome: 5000
      };
      localStorage.setItem('swissBudgetPro_data', JSON.stringify(data));
    });
    
    await page.reload();
    await dashboardPage.waitForPageLoad();
    
    // Should fallback to default canton (ZH)
    const cantonSelect = dashboardPage.cantonSelect;
    const selectedValue = await cantonSelect.inputValue();
    
    // Should be a valid canton code
    expect(['ZH', 'BE', 'LU', 'UR', 'SZ', 'OW', 'NW', 'GL', 'ZG', 'FR', 'SO', 'BS', 'BL', 'SH', 'AR', 'AI', 'SG', 'GR', 'AG', 'TG', 'TI', 'VD', 'VS', 'NE', 'GE', 'JU']).toContain(selectedValue);
    
    console.log('✅ Invalid canton handling verified');
  });

  test('Edge case: Network offline behavior', async ({ page, context }) => {
    console.log('📡 Testing offline behavior');
    
    const scenario = swissTestScenarios.baselTechWorker;
    await dashboardPage.inputScenario(scenario);
    
    // Simulate offline
    await context.setOffline(true);
    
    // App should continue to work (it's client-side)
    await dashboardPage.enterMonthlyIncome(scenario.income.monthly + 1000);
    await dashboardPage.waitForCalculations();
    
    // Calculations should still work
    await expect(dashboardPage.fireProjection).toBeVisible();
    await expect(dashboardPage.monthlyTaxAmount).toBeVisible();
    
    // Navigate between tabs
    await dashboardPage.navigateToTaxOptimizationTab();
    await dashboardPage.navigateToSwissRelocationTab();
    
    // Should still be functional
    await expect(dashboardPage.page.locator('[data-testid="relocation-analysis"]')).toBeVisible();
    
    // Go back online
    await context.setOffline(false);
    
    console.log('✅ Offline behavior verified');
  });

  test('Edge case: Rapid input changes', async ({ page }) => {
    console.log('⚡ Testing rapid input changes');
    
    // Rapidly change income values
    const incomes = [5000, 10000, 15000, 8000, 12000, 6000];
    
    for (const income of incomes) {
      await dashboardPage.enterMonthlyIncome(income);
      // Don't wait for calculations to complete - test rapid changes
      await page.waitForTimeout(100);
    }
    
    // Wait for final calculation
    await dashboardPage.waitForCalculations();
    
    // Should show the last entered value
    const finalIncome = await dashboardPage.monthlyIncomeInput.inputValue();
    expect(finalIncome).toBe('6000');
    
    // Calculations should be stable
    await expect(dashboardPage.fireProjection).toBeVisible();
    
    console.log('✅ Rapid input changes handled');
  });

  test('Edge case: Browser storage disabled', async ({ browser }) => {
    console.log('🚫 Testing disabled storage');
    
    // Create context with storage disabled
    const context = await browser.newContext({
      storageState: undefined
    });
    
    const page = await context.newPage();
    
    // Disable localStorage
    await page.addInitScript(() => {
      Object.defineProperty(window, 'localStorage', {
        value: null,
        writable: false
      });
    });
    
    const dashboard = new DashboardPage(page);
    await dashboard.goto();
    await dashboard.waitForPageLoad();
    
    // App should still function without localStorage
    const scenario = swissTestScenarios.zurichProfessional;
    await dashboard.inputScenario(scenario);
    
    await expect(dashboard.fireProjection).toBeVisible();
    await expect(dashboard.monthlyTaxAmount).toBeVisible();
    
    await context.close();
    console.log('✅ Disabled storage handling verified');
  });

  test('Edge case: Very long session', async ({ page }) => {
    console.log('⏰ Testing long session stability');
    
    const scenario = swissTestScenarios.vaudFamily;
    
    // Simulate a long session with many operations
    for (let i = 0; i < 10; i++) {
      await dashboardPage.inputScenario(scenario);
      
      // Navigate through all tabs
      await dashboardPage.navigateToOverviewTab();
      await dashboardPage.navigateToBudgetTab();
      await dashboardPage.navigateToTaxOptimizationTab();
      await dashboardPage.navigateToSwissRelocationTab();
      
      // Modify values
      await dashboardPage.enterMonthlyIncome(scenario.income.monthly + (i * 500));
      await dashboardPage.selectCanton(['ZH', 'ZG', 'GE', 'VD'][i % 4]);
      
      await dashboardPage.waitForCalculations();
      
      console.log(`✅ Long session iteration ${i + 1} completed`);
    }
    
    // App should still be responsive
    await expect(dashboardPage.fireProjection).toBeVisible();
    await expect(dashboardPage.monthlyTaxAmount).toBeVisible();
    
    console.log('✅ Long session stability verified');
  });

  test('Edge case: Special characters in data', async ({ page }) => {
    console.log('🔤 Testing special characters');
    
    // Test with special characters in localStorage
    await page.evaluate(() => {
      const data = {
        customNote: 'Special chars: äöü ñ 中文 🎉 €$£¥',
        monthlyIncome: 5000,
        selectedCanton: 'ZH'
      };
      localStorage.setItem('swissBudgetPro_data', JSON.stringify(data));
    });
    
    await page.reload();
    await dashboardPage.waitForPageLoad();
    
    // App should handle special characters gracefully
    const scenario = swissTestScenarios.bernConservative;
    await dashboardPage.inputScenario(scenario);
    
    await expect(dashboardPage.fireProjection).toBeVisible();
    
    // Verify data integrity
    const storedData = await page.evaluate(() => {
      const data = localStorage.getItem('swissBudgetPro_data');
      return data ? JSON.parse(data) : null;
    });
    
    expect(storedData).toBeTruthy();
    
    console.log('✅ Special characters handling verified');
  });
});
