import { Locator, Page, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';

/**
 * Enhanced Base Page Object Model for Swiss Budget Pro
 * Contains common functionality shared across all pages with comprehensive logging
 */
export class BasePage {
  protected page: Page;
  protected testHelpers: TestHelpers;

  constructor(page: Page) {
    this.page = page;
    this.testHelpers = new TestHelpers(page);
  }

  // Common locators
  get loadingSpinner(): Locator {
    return this.page.locator('[data-testid="loading-spinner"]');
  }

  get errorMessage(): Locator {
    return this.page.locator('[data-testid="error-message"]');
  }

  get successMessage(): Locator {
    return this.page.locator('[data-testid="success-message"]');
  }

  get languageSwitcher(): Locator {
    return this.page.locator('[data-testid="language-switcher"]');
  }

  get darkModeToggle(): Locator {
    return this.page.locator('[data-testid="dark-mode-toggle"]');
  }

  // Navigation methods
  async goto(path: string = '/'): Promise<void> {
    console.log(`🔗 Navigating to: ${path}`);
    await this.page.goto(path);
    await this.waitForPageLoad();
  }

  async waitForPageLoad(): Promise<void> {
    console.log('⏳ Waiting for Swiss Budget Pro to load...');

    try {
      // Use the enhanced test helper for comprehensive loading
      await this.testHelpers.waitForSwissBudgetProLoad();
      console.log('✅ Swiss Budget Pro loaded successfully');
    } catch (error) {
      console.error('❌ Failed to load Swiss Budget Pro:', error.message);

      // Capture debug information on failure
      await this.testHelpers.captureDebugInfo('page-load-failure');

      // Generate a test report for debugging
      await this.testHelpers.generateTestReport('page-load-failure');

      throw error;
    }
  }

  // Language switching
  async switchLanguage(language: 'en' | 'de'): Promise<void> {
    await this.languageSwitcher.click();
    await this.page.locator(`[data-testid="language-option-${language}"]`).click();

    // Wait for language change to take effect
    await this.page.waitForTimeout(500);
  }

  // Theme switching
  async toggleDarkMode(): Promise<void> {
    await this.darkModeToggle.click();
    await this.page.waitForTimeout(300); // Animation time
  }

  // Form helpers
  async fillInput(selector: string, value: string): Promise<void> {
    const input = this.page.locator(selector);
    await input.clear();
    await input.fill(value);

    // Trigger blur to ensure auto-save
    await input.blur();
    await this.page.waitForTimeout(100);
  }

  async selectOption(selector: string, value: string): Promise<void> {
    await this.page.locator(selector).selectOption(value);
    await this.page.waitForTimeout(100);
  }

  // Wait helpers
  async waitForCalculations(): Promise<void> {
    // Wait for any calculation spinners to disappear
    await this.page.waitForFunction(() => {
      const calculationElements = document.querySelectorAll('[data-testid*="calculation"]');
      return Array.from(calculationElements).every(el =>
        !el.textContent?.includes('Calculating...') &&
        !el.classList.contains('loading')
      );
    }, { timeout: 10000 });
  }

  async waitForAutoSave(): Promise<void> {
    // Wait for auto-save indicator
    await this.page.waitForFunction(() => {
      const saveIndicator = document.querySelector('[data-testid="save-status"]');
      return saveIndicator?.textContent?.includes('Saved') ||
             saveIndicator?.textContent?.includes('Gespeichert');
    }, { timeout: 5000 }).catch(() => {
      // Ignore timeout - auto-save might not have indicator
    });
  }

  // Assertion helpers
  async expectVisible(selector: string): Promise<void> {
    await expect(this.page.locator(selector)).toBeVisible();
  }

  async expectHidden(selector: string): Promise<void> {
    await expect(this.page.locator(selector)).toBeHidden();
  }

  async expectText(selector: string, text: string): Promise<void> {
    await expect(this.page.locator(selector)).toContainText(text);
  }

  async expectValue(selector: string, value: string): Promise<void> {
    await expect(this.page.locator(selector)).toHaveValue(value);
  }

  // Swiss-specific helpers
  async expectSwissCurrency(selector: string, amount: number): Promise<void> {
    const formattedAmount = new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF'
    }).format(amount);

    await this.expectText(selector, formattedAmount);
  }

  async expectSwissPercentage(selector: string, percentage: number): Promise<void> {
    const formattedPercentage = new Intl.NumberFormat('de-CH', {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 1
    }).format(percentage / 100);

    await this.expectText(selector, formattedPercentage);
  }

  // Screenshot helpers
  async takeScreenshot(name: string): Promise<void> {
    await this.page.screenshot({
      path: `test-results/screenshots/${name}.png`,
      fullPage: true
    });
  }

  // Local storage helpers
  async clearLocalStorage(): Promise<void> {
    await this.page.evaluate(() => {
      localStorage.clear();
    });
  }

  async getLocalStorageItem(key: string): Promise<string | null> {
    return await this.page.evaluate((key) => {
      return localStorage.getItem(key);
    }, key);
  }

  async setLocalStorageItem(key: string, value: string): Promise<void> {
    await this.page.evaluate(({ key, value }) => {
      localStorage.setItem(key, value);
    }, { key, value });
  }

  // Error handling
  async expectNoErrors(): Promise<void> {
    // Check for JavaScript errors
    const errors = await this.page.evaluate(() => {
      return (window as any).jsErrors || [];
    });

    expect(errors).toHaveLength(0);

    // Check for visible error messages
    const errorElements = await this.page.locator('[data-testid*="error"]').count();
    expect(errorElements).toBe(0);
  }

  // Performance helpers
  async measurePageLoadTime(): Promise<number> {
    const startTime = Date.now();
    await this.waitForPageLoad();
    return Date.now() - startTime;
  }

  // Accessibility helpers
  async checkAccessibility(): Promise<void> {
    // Basic accessibility checks
    await expect(this.page.locator('h1')).toBeVisible(); // Page should have a heading

    // Check for alt text on images
    const images = await this.page.locator('img').count();
    if (images > 0) {
      const imagesWithAlt = await this.page.locator('img[alt]').count();
      expect(imagesWithAlt).toBe(images);
    }

    // Check for form labels
    const inputs = await this.page.locator('input').count();
    if (inputs > 0) {
      const inputsWithLabels = await this.page.locator('input[aria-label], input[aria-labelledby], label input').count();
      expect(inputsWithLabels).toBeGreaterThan(0);
    }
  }
}
