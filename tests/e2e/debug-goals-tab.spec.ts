import { test, expect } from "@playwright/test";

test.describe("Debug Navigation", () => {
  test("Check if age input is visible in goals sub-tab", async ({ page }) => {
    // Navigate to the app
    await page.goto("/");

    // Wait for the app to load
    await expect(page.locator("h1")).toContainText("Swiss Budget Pro");

    // Navigate to Planning tab
    console.log("🔗 Clicking Planning tab");
    await page.click('[data-testid="tab-planning"]');
    await page.waitForTimeout(1000);

    // Navigate to Goals sub-tab
    console.log("🔗 Clicking Goals sub-tab");
    await page.click('[data-testid="secondary-tab-goals"]');
    await page.waitForTimeout(1000);

    // Check if age input is visible
    console.log("🔍 Looking for age input");
    const ageInput = page.locator('[data-testid="age-input"]');

    // Debug: Check if the element exists
    const exists = await ageInput.count();
    console.log(`📊 Age input count: ${exists}`);

    if (exists > 0) {
      const isVisible = await ageInput.isVisible();
      console.log(`👁️ Age input visible: ${isVisible}`);

      if (isVisible) {
        const value = await ageInput.inputValue();
        console.log(`💰 Age input value: ${value}`);
      }
    }

    // Expect the age input to be visible
    await expect(ageInput).toBeVisible();
  });

  test("Check if canton select is visible in tax optimization sub-tab", async ({
    page,
  }) => {
    // Navigate to the app
    await page.goto("/");

    // Wait for the app to load
    await expect(page.locator("h1")).toContainText("Swiss Budget Pro");

    // Navigate to Analysis tab
    console.log("🔗 Clicking Analysis tab");
    await page.click('[data-testid="tab-analysis"]');
    await page.waitForTimeout(1000);

    // Navigate to Tax Optimization sub-tab
    console.log("🔗 Clicking Tax Optimization sub-tab");
    await page.click('[data-testid="secondary-tab-taxOptimization"]');
    await page.waitForTimeout(1000);

    // Check if canton select is visible
    console.log("🔍 Looking for canton select");
    const cantonSelect = page.locator('[data-testid="canton-select"]');

    // Debug: Check if the element exists
    const exists = await cantonSelect.count();
    console.log(`📊 Canton select count: ${exists}`);

    if (exists > 0) {
      const isVisible = await cantonSelect.isVisible();
      console.log(`👁️ Canton select visible: ${isVisible}`);

      if (isVisible) {
        const value = await cantonSelect.inputValue();
        console.log(`🏛️ Canton select value: ${value}`);
      }
    }

    // Take a screenshot for debugging
    await page.screenshot({
      path: "debug-tax-optimization-tab.png",
      fullPage: true,
    });

    // List all visible selects for debugging
    const allSelects = await page.locator("select").all();
    console.log(`📝 Total selects found: ${allSelects.length}`);

    for (let i = 0; i < allSelects.length; i++) {
      const select = allSelects[i];
      const testId = await select.getAttribute("data-testid");
      const isVisible = await select.isVisible();
      if (testId && isVisible) {
        console.log(
          `📋 Select ${i}: data-testid="${testId}" visible=${isVisible}`
        );
      }
    }

    // Expect the canton select to be visible
    await expect(cantonSelect).toBeVisible();
  });
});
