/**
 * Healthcare optimization test scenarios for Swiss Budget Pro
 * Comprehensive test data for various Swiss healthcare situations
 */

import { HealthProfileData } from "../pages/healthcare-optimizer-page";

export interface HealthcareTestScenario {
  name: string;
  description: string;
  profile: HealthProfileData;
  expectedResults: {
    minSavings: number;
    maxSavings: number;
    optimalDeductible: number;
    riskLevel: "low" | "medium" | "high";
    fireImpact: {
      additionalCapital: number;
      delayMonths: number;
      healthcarePercentage: number;
    };
  };
  testCriteria: {
    shouldOptimizeDeductible: boolean;
    shouldRecommendInsuranceSwitch: boolean;
    shouldQualifyForSubsidies: boolean;
    expectedRecommendationCount: number;
  };
}

export const healthcareTestScenarios: Record<string, HealthcareTestScenario> = {
  // Young Professional - High Deductible Strategy
  youngProfessionalZurich: {
    name: "Young Professional in Zurich",
    description: "25-year-old healthy professional with high risk tolerance",
    profile: {
      age: 25,
      canton: "ZH",
      income: 75000,
      healthStatus: "excellent",
      familySize: 1,
      hasChildren: false,
      currentPremium: 320,
      currentDeductible: 300,
      expectedMedicalExpenses: 600,
      riskTolerance: "high"
    },
    expectedResults: {
      minSavings: 800,
      maxSavings: 1500,
      optimalDeductible: 2500,
      riskLevel: "low",
      fireImpact: {
        additionalCapital: 15000,
        delayMonths: 6,
        healthcarePercentage: 8
      }
    },
    testCriteria: {
      shouldOptimizeDeductible: true,
      shouldRecommendInsuranceSwitch: true,
      shouldQualifyForSubsidies: false,
      expectedRecommendationCount: 3
    }
  },

  // Family with Children - Balanced Strategy
  familyBern: {
    name: "Family in Bern",
    description: "35-year-old family with 2 children, moderate health expenses",
    profile: {
      age: 35,
      canton: "BE",
      income: 95000,
      healthStatus: "good",
      familySize: 4,
      hasChildren: true,
      currentPremium: 380,
      currentDeductible: 1000,
      expectedMedicalExpenses: 1800,
      riskTolerance: "medium"
    },
    expectedResults: {
      minSavings: 600,
      maxSavings: 1200,
      optimalDeductible: 1500,
      riskLevel: "medium",
      fireImpact: {
        additionalCapital: 45000,
        delayMonths: 18,
        healthcarePercentage: 15
      }
    },
    testCriteria: {
      shouldOptimizeDeductible: true,
      shouldRecommendInsuranceSwitch: true,
      shouldQualifyForSubsidies: false,
      expectedRecommendationCount: 3
    }
  },

  // High-Income Geneva Executive - Premium Strategy
  genevaExecutive: {
    name: "Geneva Executive",
    description: "45-year-old high-income executive with health concerns",
    profile: {
      age: 45,
      canton: "GE",
      income: 150000,
      healthStatus: "fair",
      familySize: 2,
      hasChildren: false,
      currentPremium: 450,
      currentDeductible: 300,
      expectedMedicalExpenses: 3500,
      riskTolerance: "low"
    },
    expectedResults: {
      minSavings: 200,
      maxSavings: 800,
      optimalDeductible: 500,
      riskLevel: "high",
      fireImpact: {
        additionalCapital: 85000,
        delayMonths: 24,
        healthcarePercentage: 12
      }
    },
    testCriteria: {
      shouldOptimizeDeductible: false,
      shouldRecommendInsuranceSwitch: true,
      shouldQualifyForSubsidies: false,
      expectedRecommendationCount: 3
    }
  },

  // Low-Income Subsidy Eligible - Cost Minimization
  lowIncomeVaud: {
    name: "Low-Income Vaud Resident",
    description: "30-year-old with low income, subsidy eligible",
    profile: {
      age: 30,
      canton: "VD",
      income: 32000,
      healthStatus: "good",
      familySize: 1,
      hasChildren: false,
      currentPremium: 380,
      currentDeductible: 300,
      expectedMedicalExpenses: 1000,
      riskTolerance: "low"
    },
    expectedResults: {
      minSavings: 1500,
      maxSavings: 3000,
      optimalDeductible: 300,
      riskLevel: "low",
      fireImpact: {
        additionalCapital: 25000,
        delayMonths: 12,
        healthcarePercentage: 20
      }
    },
    testCriteria: {
      shouldOptimizeDeductible: false,
      shouldRecommendInsuranceSwitch: true,
      shouldQualifyForSubsidies: true,
      expectedRecommendationCount: 3
    }
  },

  // Senior Pre-Retirement - Conservative Strategy
  seniorBasel: {
    name: "Senior in Basel",
    description: "58-year-old approaching retirement with health issues",
    profile: {
      age: 58,
      canton: "BS",
      income: 85000,
      healthStatus: "poor",
      familySize: 2,
      hasChildren: false,
      currentPremium: 420,
      currentDeductible: 1000,
      expectedMedicalExpenses: 4500,
      riskTolerance: "low"
    },
    expectedResults: {
      minSavings: 100,
      maxSavings: 600,
      optimalDeductible: 300,
      riskLevel: "high",
      fireImpact: {
        additionalCapital: 120000,
        delayMonths: 36,
        healthcarePercentage: 25
      }
    },
    testCriteria: {
      shouldOptimizeDeductible: false,
      shouldRecommendInsuranceSwitch: true,
      shouldQualifyForSubsidies: false,
      expectedRecommendationCount: 3
    }
  },

  // FIRE Candidate - Optimization Focus
  fireCandidate: {
    name: "FIRE Candidate",
    description: "40-year-old planning early retirement, optimization focused",
    profile: {
      age: 40,
      canton: "ZG",
      income: 120000,
      healthStatus: "excellent",
      familySize: 1,
      hasChildren: false,
      currentPremium: 350,
      currentDeductible: 1000,
      expectedMedicalExpenses: 800,
      riskTolerance: "high"
    },
    expectedResults: {
      minSavings: 1000,
      maxSavings: 2000,
      optimalDeductible: 2500,
      riskLevel: "low",
      fireImpact: {
        additionalCapital: 35000,
        delayMonths: 8,
        healthcarePercentage: 10
      }
    },
    testCriteria: {
      shouldOptimizeDeductible: true,
      shouldRecommendInsuranceSwitch: true,
      shouldQualifyForSubsidies: false,
      expectedRecommendationCount: 3
    }
  },

  // Chronic Condition - Specialized Strategy
  chronicConditionTicino: {
    name: "Chronic Condition Patient",
    description: "42-year-old with chronic condition requiring regular treatment",
    profile: {
      age: 42,
      canton: "TI",
      income: 70000,
      healthStatus: "poor",
      familySize: 1,
      hasChildren: false,
      currentPremium: 360,
      currentDeductible: 2500,
      expectedMedicalExpenses: 6000,
      riskTolerance: "low"
    },
    expectedResults: {
      minSavings: 500,
      maxSavings: 1500,
      optimalDeductible: 300,
      riskLevel: "high",
      fireImpact: {
        additionalCapital: 150000,
        delayMonths: 48,
        healthcarePercentage: 30
      }
    },
    testCriteria: {
      shouldOptimizeDeductible: true,
      shouldRecommendInsuranceSwitch: true,
      shouldQualifyForSubsidies: false,
      expectedRecommendationCount: 3
    }
  },

  // Student - Budget Strategy
  studentLausanne: {
    name: "Student in Lausanne",
    description: "22-year-old student with minimal income",
    profile: {
      age: 22,
      canton: "VD",
      income: 18000,
      healthStatus: "excellent",
      familySize: 1,
      hasChildren: false,
      currentPremium: 280,
      currentDeductible: 300,
      expectedMedicalExpenses: 400,
      riskTolerance: "medium"
    },
    expectedResults: {
      minSavings: 2000,
      maxSavings: 4000,
      optimalDeductible: 2500,
      riskLevel: "low",
      fireImpact: {
        additionalCapital: 10000,
        delayMonths: 3,
        healthcarePercentage: 25
      }
    },
    testCriteria: {
      shouldOptimizeDeductible: true,
      shouldRecommendInsuranceSwitch: true,
      shouldQualifyForSubsidies: true,
      expectedRecommendationCount: 3
    }
  },

  // Cross-Border Worker - Special Considerations
  crossBorderWorker: {
    name: "Cross-Border Worker",
    description: "35-year-old working in Geneva, living in France",
    profile: {
      age: 35,
      canton: "GE",
      income: 90000,
      healthStatus: "good",
      familySize: 3,
      hasChildren: true,
      currentPremium: 420,
      currentDeductible: 1000,
      expectedMedicalExpenses: 1500,
      riskTolerance: "medium"
    },
    expectedResults: {
      minSavings: 800,
      maxSavings: 1600,
      optimalDeductible: 1500,
      riskLevel: "medium",
      fireImpact: {
        additionalCapital: 50000,
        delayMonths: 20,
        healthcarePercentage: 18
      }
    },
    testCriteria: {
      shouldOptimizeDeductible: true,
      shouldRecommendInsuranceSwitch: true,
      shouldQualifyForSubsidies: false,
      expectedRecommendationCount: 3
    }
  },

  // Rural vs Urban Comparison
  ruralGraubunden: {
    name: "Rural Graubünden Resident",
    description: "50-year-old living in rural area with limited healthcare access",
    profile: {
      age: 50,
      canton: "GR",
      income: 65000,
      healthStatus: "good",
      familySize: 2,
      hasChildren: false,
      currentPremium: 340,
      currentDeductible: 1000,
      expectedMedicalExpenses: 1200,
      riskTolerance: "medium"
    },
    expectedResults: {
      minSavings: 600,
      maxSavings: 1200,
      optimalDeductible: 1500,
      riskLevel: "medium",
      fireImpact: {
        additionalCapital: 40000,
        delayMonths: 15,
        healthcarePercentage: 16
      }
    },
    testCriteria: {
      shouldOptimizeDeductible: true,
      shouldRecommendInsuranceSwitch: true,
      shouldQualifyForSubsidies: false,
      expectedRecommendationCount: 3
    }
  }
};

// Canton-specific test scenarios for geographic arbitrage testing
export const cantonComparisonScenarios = {
  standardProfile: {
    age: 35,
    income: 80000,
    healthStatus: "good" as const,
    familySize: 1,
    hasChildren: false,
    currentPremium: 350,
    currentDeductible: 1000,
    expectedMedicalExpenses: 1200,
    riskTolerance: "medium" as const
  },
  
  expectedCantonRankings: [
    { canton: "JU", expectedSavings: 2000 },
    { canton: "VS", expectedSavings: 1800 },
    { canton: "TI", expectedSavings: 1600 },
    { canton: "GR", expectedSavings: 1400 },
    { canton: "VD", expectedSavings: 1200 },
    { canton: "BE", expectedSavings: 1000 },
    { canton: "AG", expectedSavings: 800 },
    { canton: "LU", expectedSavings: 600 },
    { canton: "ZH", expectedSavings: 400 },
    { canton: "BS", expectedSavings: 200 },
    { canton: "GE", expectedSavings: 0 }
  ]
};

// Edge case scenarios for testing validation and error handling
export const edgeCaseScenarios = {
  minimumAge: {
    ...healthcareTestScenarios.youngProfessionalZurich.profile,
    age: 18
  },
  
  maximumAge: {
    ...healthcareTestScenarios.seniorBasel.profile,
    age: 99
  },
  
  minimumIncome: {
    ...healthcareTestScenarios.studentLausanne.profile,
    income: 0
  },
  
  maximumIncome: {
    ...healthcareTestScenarios.genevaExecutive.profile,
    income: 1000000
  },
  
  largeFamily: {
    ...healthcareTestScenarios.familyBern.profile,
    familySize: 8,
    hasChildren: true
  },
  
  extremeExpenses: {
    ...healthcareTestScenarios.chronicConditionTicino.profile,
    expectedMedicalExpenses: 15000
  }
};

// Performance test scenarios
export const performanceTestScenarios = {
  rapidCalculation: {
    profiles: [
      healthcareTestScenarios.youngProfessionalZurich.profile,
      healthcareTestScenarios.familyBern.profile,
      healthcareTestScenarios.genevaExecutive.profile,
      healthcareTestScenarios.fireCandidate.profile
    ],
    maxCalculationTime: 2000 // milliseconds
  },
  
  cantonSwitching: {
    baseProfile: healthcareTestScenarios.youngProfessionalZurich.profile,
    cantons: ["ZH", "GE", "VD", "BE", "BS", "AG", "LU", "TI"],
    maxSwitchTime: 1000 // milliseconds per switch
  }
};
