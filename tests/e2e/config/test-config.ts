/**
 * Comprehensive Test Configuration for Swiss Budget Pro E2E Tests
 * Defines test categories, priorities, and execution strategies
 */

export interface TestCategory {
  name: string;
  description: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  timeout: number;
  retries: number;
  parallel: boolean;
  browsers: string[];
  tags: string[];
}

export interface TestSuite {
  name: string;
  categories: TestCategory[];
  globalTimeout: number;
  setupTimeout: number;
  teardownTimeout: number;
}

/**
 * Test Categories Configuration
 */
export const testCategories: Record<string, TestCategory> = {
  smoke: {
    name: 'Smoke Tests',
    description: 'Basic functionality verification',
    priority: 'critical',
    timeout: 30000,
    retries: 2,
    parallel: true,
    browsers: ['chromium', 'firefox', 'webkit'],
    tags: ['smoke', 'critical', 'fast']
  },

  criticalPath: {
    name: 'Critical Path Tests',
    description: 'Core user journeys and workflows',
    priority: 'critical',
    timeout: 60000,
    retries: 2,
    parallel: false,
    browsers: ['chromium', 'firefox'],
    tags: ['critical-path', 'user-journey', 'core']
  },

  formValidation: {
    name: 'Form Validation Tests',
    description: 'Input validation and error handling',
    priority: 'high',
    timeout: 45000,
    retries: 1,
    parallel: true,
    browsers: ['chromium'],
    tags: ['validation', 'forms', 'input']
  },

  mobileResponsive: {
    name: 'Mobile Responsiveness Tests',
    description: 'Mobile and tablet layout testing',
    priority: 'high',
    timeout: 45000,
    retries: 1,
    parallel: true,
    browsers: ['chromium', 'webkit'],
    tags: ['mobile', 'responsive', 'ui']
  },

  dataManagement: {
    name: 'Data Import/Export Tests',
    description: 'Data persistence and transfer functionality',
    priority: 'high',
    timeout: 60000,
    retries: 1,
    parallel: false,
    browsers: ['chromium'],
    tags: ['data', 'import', 'export', 'persistence']
  },

  errorHandling: {
    name: 'Error Handling Tests',
    description: 'Edge cases and error recovery',
    priority: 'medium',
    timeout: 45000,
    retries: 1,
    parallel: true,
    browsers: ['chromium'],
    tags: ['error-handling', 'edge-cases', 'recovery']
  },

  visualRegression: {
    name: 'Visual Regression Tests',
    description: 'UI consistency and visual changes',
    priority: 'medium',
    timeout: 60000,
    retries: 0,
    parallel: false,
    browsers: ['chromium'],
    tags: ['visual', 'ui', 'regression']
  },

  accessibility: {
    name: 'Accessibility Tests',
    description: 'WCAG compliance and accessibility features',
    priority: 'high',
    timeout: 45000,
    retries: 1,
    parallel: true,
    browsers: ['chromium', 'firefox'],
    tags: ['accessibility', 'a11y', 'wcag']
  },

  performance: {
    name: 'Performance Tests',
    description: 'Load times, calculations, and optimization',
    priority: 'medium',
    timeout: 90000,
    retries: 1,
    parallel: false,
    browsers: ['chromium'],
    tags: ['performance', 'optimization', 'speed']
  },

  userJourneys: {
    name: 'Complete User Journey Tests',
    description: 'End-to-end user workflows and scenarios',
    priority: 'high',
    timeout: 120000,
    retries: 1,
    parallel: false,
    browsers: ['chromium'],
    tags: ['user-journey', 'workflow', 'scenario']
  },

  swissFeatures: {
    name: 'Swiss-Specific Feature Tests',
    description: 'Swiss tax, canton, and financial features',
    priority: 'critical',
    timeout: 60000,
    retries: 2,
    parallel: true,
    browsers: ['chromium', 'firefox'],
    tags: ['swiss', 'tax', 'canton', 'financial']
  },

  internationalization: {
    name: 'Internationalization Tests',
    description: 'Multi-language support and localization',
    priority: 'medium',
    timeout: 45000,
    retries: 1,
    parallel: true,
    browsers: ['chromium'],
    tags: ['i18n', 'localization', 'language']
  }
};

/**
 * Test Suites Configuration
 */
export const testSuites: Record<string, TestSuite> = {
  full: {
    name: 'Full Test Suite',
    categories: Object.values(testCategories),
    globalTimeout: 180000, // 3 minutes
    setupTimeout: 60000,
    teardownTimeout: 30000
  },

  critical: {
    name: 'Critical Tests Only',
    categories: Object.values(testCategories).filter(cat => cat.priority === 'critical'),
    globalTimeout: 120000, // 2 minutes
    setupTimeout: 30000,
    teardownTimeout: 15000
  },

  smoke: {
    name: 'Smoke Tests Only',
    categories: [testCategories.smoke],
    globalTimeout: 60000, // 1 minute
    setupTimeout: 15000,
    teardownTimeout: 10000
  },

  regression: {
    name: 'Regression Test Suite',
    categories: [
      testCategories.smoke,
      testCategories.criticalPath,
      testCategories.formValidation,
      testCategories.swissFeatures
    ],
    globalTimeout: 150000, // 2.5 minutes
    setupTimeout: 45000,
    teardownTimeout: 20000
  },

  ui: {
    name: 'UI-Focused Test Suite',
    categories: [
      testCategories.mobileResponsive,
      testCategories.visualRegression,
      testCategories.accessibility
    ],
    globalTimeout: 120000, // 2 minutes
    setupTimeout: 30000,
    teardownTimeout: 15000
  },

  performance: {
    name: 'Performance Test Suite',
    categories: [testCategories.performance],
    globalTimeout: 180000, // 3 minutes
    setupTimeout: 60000,
    teardownTimeout: 30000
  }
};

/**
 * Environment-specific configurations
 */
export const environmentConfigs = {
  development: {
    baseURL: 'http://localhost:5173',
    timeout: 30000,
    retries: 0,
    workers: 4,
    headless: false,
    slowMo: 100
  },

  staging: {
    baseURL: 'https://staging.swissbudgetpro.com',
    timeout: 45000,
    retries: 1,
    workers: 2,
    headless: true,
    slowMo: 0
  },

  production: {
    baseURL: 'https://swissbudgetpro.com',
    timeout: 60000,
    retries: 2,
    workers: 1,
    headless: true,
    slowMo: 0
  },

  ci: {
    baseURL: 'http://localhost:5173',
    timeout: 60000,
    retries: 2,
    workers: 1,
    headless: true,
    slowMo: 0
  }
};

/**
 * Test data configurations
 */
export const testDataConfig = {
  scenarios: {
    quick: ['zurichProfessional'],
    comprehensive: ['zurichProfessional', 'vaudFamily', 'genevaExecutive', 'bernConservative'],
    full: ['zurichProfessional', 'vaudFamily', 'genevaExecutive', 'bernConservative', 'baselTechWorker']
  },

  cantons: {
    major: ['ZH', 'GE', 'VD', 'BE'],
    all: ['AG', 'AI', 'AR', 'BE', 'BL', 'BS', 'FR', 'GE', 'GL', 'GR', 'JU', 'LU', 'NE', 'NW', 'OW', 'SG', 'SH', 'SO', 'SZ', 'TG', 'TI', 'UR', 'VD', 'VS', 'ZG', 'ZH']
  },

  viewports: {
    mobile: [
      { width: 375, height: 667, name: 'iPhone SE' },
      { width: 414, height: 896, name: 'iPhone 11' }
    ],
    tablet: [
      { width: 768, height: 1024, name: 'iPad' },
      { width: 1024, height: 768, name: 'iPad Landscape' }
    ],
    desktop: [
      { width: 1280, height: 720, name: 'Desktop' },
      { width: 1920, height: 1080, name: 'Desktop Large' }
    ]
  }
};

/**
 * Reporting configurations
 */
export const reportingConfig = {
  html: {
    enabled: true,
    outputDir: 'playwright-report',
    open: 'never'
  },

  json: {
    enabled: true,
    outputFile: 'playwright-report/results.json'
  },

  junit: {
    enabled: true,
    outputFile: 'playwright-report/results.xml'
  },

  allure: {
    enabled: false,
    outputDir: 'allure-results'
  },

  custom: {
    enabled: true,
    outputDir: 'test-results/custom-reports'
  }
};

/**
 * Utility functions for test configuration
 */
export class TestConfigManager {
  /**
   * Get test configuration for specific environment
   */
  static getEnvironmentConfig(env: string = 'development') {
    return environmentConfigs[env as keyof typeof environmentConfigs] || environmentConfigs.development;
  }

  /**
   * Get test suite configuration
   */
  static getTestSuite(suiteName: string = 'full') {
    return testSuites[suiteName] || testSuites.full;
  }

  /**
   * Get test categories by priority
   */
  static getCategoriesByPriority(priority: 'critical' | 'high' | 'medium' | 'low') {
    return Object.values(testCategories).filter(cat => cat.priority === priority);
  }

  /**
   * Get test categories by tag
   */
  static getCategoriesByTag(tag: string) {
    return Object.values(testCategories).filter(cat => cat.tags.includes(tag));
  }

  /**
   * Generate Playwright config for specific suite
   */
  static generatePlaywrightConfig(suiteName: string, environment: string) {
    const suite = this.getTestSuite(suiteName);
    const env = this.getEnvironmentConfig(environment);

    return {
      testDir: './tests/e2e',
      timeout: suite.globalTimeout,
      expect: { timeout: 5000 },
      fullyParallel: true,
      forbidOnly: !!process.env.CI,
      retries: env.retries,
      workers: env.workers,
      reporter: [
        ['html', { outputFolder: reportingConfig.html.outputDir }],
        ['json', { outputFile: reportingConfig.json.outputFile }],
        ['junit', { outputFile: reportingConfig.junit.outputFile }]
      ],
      use: {
        baseURL: env.baseURL,
        trace: 'on-first-retry',
        screenshot: 'only-on-failure',
        video: 'retain-on-failure',
        actionTimeout: env.timeout,
        navigationTimeout: env.timeout * 1.5,
        headless: env.headless,
        slowMo: env.slowMo
      },
      projects: suite.categories.flatMap(category => 
        category.browsers.map(browser => ({
          name: `${category.name}-${browser}`,
          use: { ...require('@playwright/test').devices[`Desktop ${browser.charAt(0).toUpperCase() + browser.slice(1)}`] },
          testMatch: `**/${category.name.toLowerCase().replace(/\s+/g, '-')}/**/*.spec.ts`,
          timeout: category.timeout,
          retries: category.retries
        }))
      )
    };
  }

  /**
   * Get test execution plan
   */
  static getExecutionPlan(suiteName: string) {
    const suite = this.getTestSuite(suiteName);
    
    return {
      totalCategories: suite.categories.length,
      criticalTests: suite.categories.filter(cat => cat.priority === 'critical').length,
      estimatedDuration: suite.categories.reduce((total, cat) => total + cat.timeout, 0),
      parallelizable: suite.categories.filter(cat => cat.parallel).length,
      sequential: suite.categories.filter(cat => !cat.parallel).length,
      browsers: [...new Set(suite.categories.flatMap(cat => cat.browsers))],
      tags: [...new Set(suite.categories.flatMap(cat => cat.tags))]
    };
  }
}

/**
 * Default export for easy importing
 */
export default {
  testCategories,
  testSuites,
  environmentConfigs,
  testDataConfig,
  reportingConfig,
  TestConfigManager
};
