import { chromium, FullConfig } from '@playwright/test';

/**
 * Global setup for Playwright tests
 * Runs once before all tests
 */
async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting Swiss Budget Pro E2E Test Suite');
  
  // Verify the application is running
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // Wait for the application to be ready
    await page.goto(config.projects[0].use?.baseURL || 'http://localhost:5173');
    await page.waitForSelector('text=Swiss Budget Pro', { timeout: 30000 });
    console.log('✅ Application is ready for testing');
  } catch (error) {
    console.error('❌ Application failed to start:', error);
    throw error;
  } finally {
    await browser.close();
  }

  // Setup test data if needed
  console.log('📊 Setting up test data...');
  
  // Clear any existing test data
  console.log('🧹 Cleaning up previous test data...');
  
  console.log('✅ Global setup completed');
}

export default globalSetup;
