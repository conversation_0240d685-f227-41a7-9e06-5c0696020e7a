name: Playwright E2E Tests

on:
  push:
    branches: [ main, develop, feature/playwright-e2e-testing ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 'lts/*'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright Browsers
      run: npx playwright install --with-deps ${{ matrix.browser }}

    - name: Build application
      run: npm run build

    - name: Run Playwright tests
      run: npx playwright test --project=${{ matrix.browser }}
      env:
        CI: true

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report-${{ matrix.browser }}
        path: playwright-report/
        retention-days: 30

    - name: Upload test screenshots
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: playwright-screenshots-${{ matrix.browser }}
        path: test-results/
        retention-days: 30

  mobile-test:
    timeout-minutes: 30
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 'lts/*'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright Browsers
      run: npx playwright install --with-deps chromium

    - name: Build application
      run: npm run build

    - name: Run Mobile Playwright tests
      run: npx playwright test --project="Mobile Chrome" --project="Mobile Safari"
      env:
        CI: true

    - name: Upload mobile test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report-mobile
        path: playwright-report/
        retention-days: 30

  performance-test:
    timeout-minutes: 30
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 'lts/*'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright Browsers
      run: npx playwright install --with-deps chromium

    - name: Build application
      run: npm run build

    - name: Run Performance tests
      run: npx playwright test tests/e2e/tests/performance/
      env:
        CI: true

    - name: Upload performance results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-performance-report
        path: playwright-report/
        retention-days: 30

  accessibility-test:
    timeout-minutes: 30
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 'lts/*'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright Browsers
      run: npx playwright install --with-deps chromium

    - name: Build application
      run: npm run build

    - name: Run Accessibility tests
      run: npx playwright test tests/e2e/tests/accessibility/
      env:
        CI: true

    - name: Upload accessibility results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-accessibility-report
        path: playwright-report/
        retention-days: 30

  report:
    if: always()
    needs: [test, mobile-test, performance-test, accessibility-test]
    runs-on: ubuntu-latest
    
    steps:
    - name: Download all artifacts
      uses: actions/download-artifact@v4
      with:
        path: artifacts

    - name: Merge test reports
      run: |
        mkdir -p merged-report
        find artifacts -name "*.json" -exec cp {} merged-report/ \;
        
    - name: Generate combined report
      run: |
        echo "# Swiss Budget Pro E2E Test Results" > test-summary.md
        echo "" >> test-summary.md
        echo "## Test Execution Summary" >> test-summary.md
        echo "- **Date**: $(date)" >> test-summary.md
        echo "- **Commit**: ${{ github.sha }}" >> test-summary.md
        echo "- **Branch**: ${{ github.ref_name }}" >> test-summary.md
        echo "" >> test-summary.md
        
        if [ -d "artifacts" ]; then
          echo "## Artifacts Generated" >> test-summary.md
          find artifacts -type f -name "*.html" | while read file; do
            echo "- [$(basename $file)]($(echo $file | sed 's|artifacts/||'))" >> test-summary.md
          done
        fi

    - name: Upload combined report
      uses: actions/upload-artifact@v4
      with:
        name: combined-test-report
        path: |
          merged-report/
          test-summary.md
        retention-days: 30

    - name: Comment PR with test results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          if (fs.existsSync('test-summary.md')) {
            const summary = fs.readFileSync('test-summary.md', 'utf8');
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: summary
            });
          }
