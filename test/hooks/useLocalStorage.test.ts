import { beforeEach, describe, expect, it, vi } from 'vitest'

// Mock the useLocalStorage hook since it's defined in retire.tsx
const mockUseLocalStorage = (key: string, defaultValue: any, options: any = {}) => {
  const { serialize = JSON.stringify, deserialize = JSON.parse, debounceMs = 500 } = options

  // Simple implementation for testing
  const isLocalStorageAvailable = () => {
    try {
      const testKey = '__localStorage_test__'
      localStorage.setItem(testKey, 'test')
      localStorage.removeItem(testKey)
      return true
    } catch {
      return false
    }
  }

  let storedValue = defaultValue

  if (isLocalStorageAvailable()) {
    try {
      const item = localStorage.getItem(key)
      storedValue = item ? deserialize(item) : defaultValue
    } catch (error) {
      storedValue = defaultValue
    }
  }

  const setValue = (value: any) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value
      storedValue = valueToStore

      if (isLocalStorageAvailable()) {
        setTimeout(() => {
          try {
            localStorage.setItem(key, serialize(valueToStore))
          } catch (error) {
            console.warn(`Error saving to localStorage key "${key}":`, error)
            if ((error as Error).name === 'QuotaExceededError') {
              console.warn('localStorage quota exceeded')
            }
          }
        }, debounceMs)
      }
    } catch (error) {
      console.warn(`Error setting value for key "${key}":`, error)
    }
  }

  return [storedValue, setValue]
}

describe('useLocalStorage Hook', () => {
  beforeEach(() => {
    localStorage.clear()
    vi.clearAllMocks()
  })

  describe('localStorage available', () => {
    it('should return default value when no stored value exists', () => {
      const [value] = mockUseLocalStorage('test-key', 'default-value')
      expect(value).toBe('default-value')
    })

    it('should return stored value when it exists', () => {
      localStorage.setItem('test-key', JSON.stringify('stored-value'))

      const [value] = mockUseLocalStorage('test-key', 'default-value')
      expect(value).toBe('stored-value')
    })

    it('should handle JSON serialization for complex objects', () => {
      const complexObject = { id: 1, name: 'test', nested: { value: 42 } }
      localStorage.setItem('complex-key', JSON.stringify(complexObject))

      const [value] = mockUseLocalStorage('complex-key', {})
      expect(value).toEqual(complexObject)
    })

    it('should use custom serialization', () => {
      const customSerialize = vi.fn(JSON.stringify)
      const customDeserialize = vi.fn(JSON.parse)

      localStorage.setItem('test-key', JSON.stringify({ count: 5 }))

      const [value] = mockUseLocalStorage('test-key', { count: 0 }, {
        serialize: customSerialize,
        deserialize: customDeserialize
      })

      expect(customDeserialize).toHaveBeenCalled()
      expect(value).toEqual({ count: 5 })
    })
  })

  describe('localStorage unavailable', () => {
    it('should fallback to default value when localStorage throws error', () => {
      // Mock localStorage to be unavailable
      const originalLocalStorage = window.localStorage
      delete (window as any).localStorage

      const [value] = mockUseLocalStorage('test-key', 'default')
      expect(value).toBe('default')

      // Restore localStorage
      window.localStorage = originalLocalStorage
    })

    it('should handle corrupted localStorage data', () => {
      localStorage.setItem('test-key', 'invalid-json{')

      const [value] = mockUseLocalStorage('test-key', 'default')
      expect(value).toBe('default')
    })
  })

  describe('error handling', () => {
    it('should handle quota exceeded error', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      // Mock localStorage.setItem to throw QuotaExceededError
      const originalSetItem = localStorage.setItem
      localStorage.setItem = vi.fn(() => {
        const error = new Error('QuotaExceededError')
        error.name = 'QuotaExceededError'
        throw error
      })

      const [, setValue] = mockUseLocalStorage('test-key', 'initial')
      setValue('new-value')

      // Wait for debounced save
      await new Promise(resolve => setTimeout(resolve, 600))

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('localStorage quota exceeded')
      )

      // Restore
      localStorage.setItem = originalSetItem
      consoleSpy.mockRestore()
    })

    it('should handle general localStorage errors', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      // Mock localStorage.setItem to throw a general error
      const originalSetItem = localStorage.setItem
      localStorage.setItem = vi.fn(() => {
        throw new Error('General storage error')
      })

      const [, setValue] = mockUseLocalStorage('test-key', 'initial')
      setValue('new-value')

      // Wait for debounced save
      await new Promise(resolve => setTimeout(resolve, 600))

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Error saving to localStorage'),
        expect.any(Error)
      )

      // Restore
      localStorage.setItem = originalSetItem
      consoleSpy.mockRestore()
    })
  })

  describe('different data types', () => {
    it('should handle different data types', () => {
      // Test with object
      const [objValue] = mockUseLocalStorage('obj-key', { name: 'test', count: 1 })
      expect(objValue).toEqual({ name: 'test', count: 1 })

      // Test with array
      const [arrValue] = mockUseLocalStorage('arr-key', [1, 2, 3])
      expect(arrValue).toEqual([1, 2, 3])

      // Test with boolean
      const [boolValue] = mockUseLocalStorage('bool-key', true)
      expect(boolValue).toBe(true)

      // Test with number
      const [numValue] = mockUseLocalStorage('num-key', 42)
      expect(numValue).toBe(42)
    })
  })
})
