import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'

// Custom render function that can be extended with providers
const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { ...options })

export * from '@testing-library/react'
export { customRender as render }

// Helper function to create mock localStorage data
export const createMockLocalStorageData = (overrides: Record<string, any> = {}) => {
  const defaultData = {
    'swissBudgetPro_darkMode': true,
    'swissBudgetPro_monthlyIncome': '10159.95',
    'swissBudgetPro_incomePercentage': 100,
    'swissBudgetPro_companyIncome': '2500',
    'swissBudgetPro_currentAge': 46,
    'swissBudgetPro_retirementAge': 55,
    'swissBudgetPro_expenses': JSON.stringify([
      { id: 1, category: 'Housing', amount: '2551', essential: true },
      { id: 2, category: 'Food', amount: '500', essential: true }
    ]),
    'swissBudgetPro_savings': JSON.stringify([
      { id: 1, goal: 'Emergency Fund', amount: '500' },
      { id: 2, goal: 'Retirement (Pillar 3a)', amount: '1280' }
    ]),
    ...overrides
  }
  
  return defaultData
}

// Helper to mock localStorage with specific data
export const mockLocalStorage = (data: Record<string, any>) => {
  const mockGetItem = vi.fn((key: string) => data[key] || null)
  const mockSetItem = vi.fn()
  
  Object.defineProperty(window, 'localStorage', {
    value: {
      getItem: mockGetItem,
      setItem: mockSetItem,
      removeItem: vi.fn(),
      clear: vi.fn(),
      length: Object.keys(data).length,
      key: vi.fn(),
    },
    writable: true,
  })
  
  return { mockGetItem, mockSetItem }
}

// Helper to simulate localStorage being unavailable
export const mockLocalStorageUnavailable = () => {
  Object.defineProperty(window, 'localStorage', {
    value: {
      getItem: vi.fn(() => { throw new Error('localStorage not available') }),
      setItem: vi.fn(() => { throw new Error('localStorage not available') }),
      removeItem: vi.fn(() => { throw new Error('localStorage not available') }),
      clear: vi.fn(() => { throw new Error('localStorage not available') }),
      length: 0,
      key: vi.fn(),
    },
    writable: true,
  })
}
