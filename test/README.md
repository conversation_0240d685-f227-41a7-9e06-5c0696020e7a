# Swiss Budget Pro - Test Suite

## Overview

This test suite provides comprehensive testing for the Swiss Budget Pro application using modern testing tools and best practices.

## Testing Stack

- **🚀 Vitest** - Fast test runner with Jest-compatible API
- **🧪 React Testing Library** - User-centric component testing
- **🌐 jsdom** - Browser environment simulation
- **📊 Coverage Reports** - Built-in code coverage analysis

## Test Structure

```
test/
├── setup.ts                 # Global test configuration
├── utils/
│   └── test-utils.tsx       # Custom render functions and helpers
├── hooks/
│   └── useLocalStorage.test.ts  # localStorage hook tests
├── components/
│   └── SwissBudgetPro.test.tsx  # Main component tests
├── calculations/
│   └── financial.test.ts    # Financial calculation tests
├── integration/
│   └── localStorage.test.tsx    # End-to-end localStorage tests
├── fixtures/
│   └── testData.ts          # Test data and scenarios
└── README.md               # This file
```

## Running Tests

### Basic Commands

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests once (CI mode)
npm run test:run

# Run with UI (visual test runner)
npm run test:ui

# Generate coverage report
npm run test:coverage
```

### Test Categories

#### Unit Tests
- **Financial Calculations** (`test/calculations/`)
  - Currency formatting
  - Savings rate calculations
  - Compound interest
  - FIRE number calculations
  - Swiss Pillar 3a validations

#### Component Tests
- **React Components** (`test/components/`)
  - Rendering with default props
  - User interactions
  - State management
  - Error boundaries

#### Integration Tests
- **localStorage Integration** (`test/integration/`)
  - Data persistence
  - Fallback behavior
  - Error handling
  - Performance

#### Hook Tests
- **Custom Hooks** (`test/hooks/`)
  - useLocalStorage functionality
  - Debouncing behavior
  - Error handling

## Test Scenarios

### Financial Scenarios
- **Young Professional** - Age 25, starting career
- **Mid-Career Family** - Age 40, family expenses
- **Near Retirement** - Age 58, wealth preservation
- **High Earner** - Aggressive FIRE strategy

### Edge Cases
- Zero income scenarios
- Negative values
- Extremely high values
- Invalid age ranges
- Corrupted localStorage data

## localStorage Testing

### Mock Implementation
```typescript
// Mock localStorage with specific data
const mockData = createMockLocalStorageData({
  'swissBudgetPro_monthlyIncome': '12000'
})
mockLocalStorage(mockData)

// Mock localStorage unavailable
mockLocalStorageUnavailable()
```

### Test Coverage
- ✅ Data persistence across sessions
- ✅ Debounced saves (500ms)
- ✅ Fallback to defaults
- ✅ Error handling (quota exceeded, corrupted data)
- ✅ Complex object serialization
- ✅ Performance optimization

## Writing New Tests

### Component Test Example
```typescript
import { render, screen } from '../utils/test-utils'
import userEvent from '@testing-library/user-event'

test('should update income when user types', async () => {
  const user = userEvent.setup()
  render(<SwissBudgetPro />)
  
  const incomeInput = screen.getByLabelText(/Primary Employment/i)
  await user.clear(incomeInput)
  await user.type(incomeInput, '15000')
  
  expect(incomeInput).toHaveValue('15000')
})
```

### Financial Calculation Test
```typescript
test('should calculate savings rate correctly', () => {
  const result = calculateSavingsRate(2000, 10000)
  expect(result).toBe(20)
})
```

## Best Practices

### 1. User-Centric Testing
- Test user interactions, not implementation details
- Use accessible queries (getByLabelText, getByRole)
- Focus on behavior, not internal state

### 2. Realistic Test Data
- Use fixtures for consistent test scenarios
- Test edge cases and error conditions
- Include Swiss-specific financial rules

### 3. Performance Testing
- Test debouncing behavior
- Verify no excessive re-renders
- Monitor localStorage operation efficiency

### 4. Error Handling
- Test localStorage unavailable scenarios
- Verify graceful degradation
- Test corrupted data recovery

## Coverage Goals

- **Statements**: > 90%
- **Branches**: > 85%
- **Functions**: > 90%
- **Lines**: > 90%

## Continuous Integration

Tests run automatically on:
- Pull requests
- Main branch commits
- Release builds

## Debugging Tests

### Visual Test Runner
```bash
npm run test:ui
```
Opens a browser-based test runner with:
- Real-time test results
- Interactive debugging
- Coverage visualization

### Debug Mode
```bash
# Run specific test file
npx vitest run test/components/SwissBudgetPro.test.tsx

# Debug with console output
npx vitest run --reporter=verbose
```

## Contributing

When adding new features:
1. Write tests first (TDD approach)
2. Ensure all existing tests pass
3. Add integration tests for complex features
4. Update fixtures for new scenarios
5. Maintain coverage thresholds

## Troubleshooting

### Common Issues

**localStorage not mocked properly**
```typescript
// Ensure setup.ts is imported in vite.config.ts
setupFiles: ['./test/setup.ts']
```

**D3 chart tests failing**
```typescript
// Mock ResizeObserver in setup.ts
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))
```

**Async tests timing out**
```typescript
// Use proper async/await patterns
await waitFor(() => {
  expect(element).toBeInTheDocument()
}, { timeout: 2000 })
```
