{ pkgs ? import (fetchTarball "https://github.com/NixOS/nixpkgs/archive/nixos-23.11.tar.gz") {} }:

pkgs.mkShell {
  name = "swiss-budget-pro-dev";

  buildInputs = with pkgs; [
    # Node.js and package managers
    nodejs_20        # Latest LTS Node.js
    npm             # npm package manager
    yarn            # Alternative package manager
    pnpm            # Fast package manager

    # Development tools
    git             # Version control
    curl            # HTTP requests
    wget            # File downloads

    # Build tools
    python3         # For native module compilation
    gcc             # C compiler for native modules
    gnumake         # Make build system
    pkg-config      # Package configuration

    # Browser testing (optional)
    chromium        # For E2E testing
    firefox         # Alternative browser

    # Development utilities
    jq              # JSON processor
    tree            # Directory tree viewer
    htop            # Process monitor

    # Text editors (optional)
    vim             # Terminal editor
    nano            # Simple editor
  ];

  # Environment variables
  shellHook = ''
    echo "🚀 Swiss Budget Pro Development Environment"
    echo "=========================================="
    echo ""
    echo "📦 Available tools:"
    echo "  • Node.js $(node --version)"
    echo "  • npm $(npm --version)"
    echo "  • yarn $(yarn --version)"
    echo "  • pnpm $(pnpm --version)"
    echo ""
    echo "🧪 Testing commands:"
    echo "  • npm test              - Run all tests"
    echo "  • npm run test:watch    - Watch mode"
    echo "  • npm run test:ui       - Visual test runner"
    echo "  • npm run test:coverage - Coverage report"
    echo "  • ./scripts/test.sh     - Custom test runner"
    echo ""
    echo "🔧 Development commands:"
    echo "  • npm run dev           - Start development server"
    echo "  • npm run build         - Build for production"
    echo "  • npm run preview       - Preview production build"
    echo ""
    echo "📁 Project structure:"
    echo "  • retire.tsx            - Main application"
    echo "  • test/                 - Test suite"
    echo "  • package.json          - Dependencies"
    echo ""

    # Set up Node.js environment
    export NODE_ENV=development
    export NPM_CONFIG_PREFIX=$PWD/.npm-global
    export PATH=$PWD/.npm-global/bin:$PATH

    # Create npm global directory if it doesn't exist
    mkdir -p .npm-global

    # Install dependencies if package.json exists and node_modules doesn't
    if [ -f package.json ] && [ ! -d node_modules ]; then
      echo "📦 Installing dependencies..."
      npm install
      echo "✅ Dependencies installed!"
      echo ""
    fi

    # Display current directory and git status
    echo "📍 Current directory: $(pwd)"
    if [ -d .git ]; then
      echo "🌿 Git branch: $(git branch --show-current 2>/dev/null || echo 'unknown')"
      echo "📊 Git status:"
      git status --porcelain | head -5
      if [ $(git status --porcelain | wc -l) -gt 5 ]; then
        echo "   ... and $(( $(git status --porcelain | wc -l) - 5 )) more files"
      fi
    fi
    echo ""
    echo "🎯 Ready to develop! Run 'npm test' to verify setup."
    echo ""
  '';

  # Additional environment setup
  NIX_SHELL_PRESERVE_PROMPT = 1;

  # Set locale for proper character encoding
  LOCALE_ARCHIVE = "${pkgs.glibcLocales}/lib/locale/locale-archive";
  LC_ALL = "en_US.UTF-8";

  # Prevent npm from checking for updates during development
  NPM_CONFIG_UPDATE_NOTIFIER = "false";

  # Use local npm cache
  NPM_CONFIG_CACHE = "./.npm-cache";

  # Optimize npm for development
  NPM_CONFIG_PROGRESS = "false";
  NPM_CONFIG_LOGLEVEL = "warn";
}
